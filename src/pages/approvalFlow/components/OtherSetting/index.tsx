import {
  CSSProperties,
  useEffect,
  useState,
  forwardRef,
  ForwardRefRenderFunction,
  useImperativeHandle,
  Fragment,
} from 'react';
import styles from './index.module.less';
import { XlbBasicForm, XlbCheckbox, XlbPopover, XlbButton } from '@xlb/components';
import ConditionModal from '../conditionModal';
import useApprovalStore from '../../store';
import { judgeListEnum } from '@xlb/components/dist/components/XlbConditionFilter/constant';
import { versionEnum } from '../../data';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { formatConditionList } from '@/pages/approvalFlow/utils.ts';
import { useShallow } from 'zustand/react/shallow';
import useFormatListData from '../../hooks/useFormat';
import { isEmpty } from 'lodash';
interface PropsType {
  info: any;
  style?: CSSProperties;
}

const OtherSetting: ForwardRefRenderFunction<any, PropsType> = ({ info, style }, ref) => {
  const { fieldList, currentVersion } = useApprovalStore(
    useShallow((state) => ({
      fieldList: state.fieldList,
      currentVersion: state.currentVersion,
    })),
  );
  const { formatCondition } = useFormatListData();
  const [visible, setVisible] = useState(false);
  const [form] = XlbBasicForm.useForm();
  const [conditionDataList, setConditionDataList] = useState<any>([]);
  const editDisabled = (currentVersion && currentVersion.version_state !== versionEnum.DESIGN) as boolean;
  useImperativeHandle(ref, () => ({
    validateData,
    returnData,
  }));

  const judgeConditionIsEmpty = (data) => {
    return data.every((it) => !!it?.conditions?.length && !!it.conditions[0]?.condition?.treeList?.length);
  };
  // 返回数据
  function returnData() {
    console.log('conditionDataList', conditionDataList);
    return {
      show: form.getFieldValue('show'),
      auto_skip: form.getFieldValue('auto_skip'),
      notified_initiator: form.getFieldValue('notified_initiator'),
      content: !judgeConditionIsEmpty(conditionDataList) ? [] : formatConditionList(conditionDataList),
      conditionDataList,
    };
  }
  // 校验数据
  function validateData() {
    return true;
  }

  // 初始化数据
  useEffect(() => {
    if (info && Object.keys(info).length) {
      const baseInfo = info.base_info;
      form.setFieldsValue({
        show: baseInfo?.show,
        auto_skip: baseInfo?.auto_skip,
        notified_initiator: baseInfo?.notified_initiator,
      });
    }
  }, [info]);

  useEffect(() => {
    if (info && info?.process && fieldList?.length) {
      const list = JSON.parse(info.process)?.conditionDataList || [];
      const formatList = formatCondition(list);
      formatList?.forEach((item: any) => {
        item.conditions[0].condition.treeList.forEach((treeItem: any) => {
          const value = treeItem.children[0].xField.value;
          treeItem.children[0].xField.yOptions = fieldList
            .find((f: any) => value === f.business_field)
            ?.options?.map((o) => ({ value: o.business_field, label: o.name }));
        });
      });
      setConditionDataList(list);
    }
  }, [info, fieldList]);
  const content = (
    <div>
      <p>同一审批人在流程中重复出现时</p>
      <p>仅审批一次,后续重复的审批节点均自动同意</p>
    </div>
  );
  return (
    <>
      <div className={styles.container} style={{ ...style }}>
        <div className={styles.form}>
          <XlbBasicForm layout="horizontal" colon={false} form={form} onFinish={(value) => {}}>
            <XlbBasicForm.Item valuePropName="checked" label="审批设置" name="show">
              <XlbCheckbox disabled={editDisabled} style={{ width: 536 }}>
                当字段值为空时候，隐藏整个字段
              </XlbCheckbox>
            </XlbBasicForm.Item>

            <XlbBasicForm.Item valuePropName="checked" style={{ marginLeft: 120 }} label="" name="auto_skip">
              <XlbCheckbox disabled={editDisabled} style={{ width: 536 }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div>审批人去重 </div>
                  <div className={styles.popver}>
                    <XlbPopover placement="right" content={content} title="">
                      <QuestionCircleOutlined style={{ fontSize: '16px' }}></QuestionCircleOutlined>
                    </XlbPopover>
                  </div>
                </div>
              </XlbCheckbox>
            </XlbBasicForm.Item>
            <XlbBasicForm.Item valuePropName="checked" style={{ marginLeft: 120 }} label="" name="notified_initiator">
              <XlbCheckbox disabled={editDisabled} style={{ width: 536 }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div>审批通过后，消息提示发起人</div>
                </div>
              </XlbCheckbox>
            </XlbBasicForm.Item>

            <XlbBasicForm.Item label="触发审批流" name="approval_data_id">
              <XlbButton
                onClick={() => {
                  !editDisabled && setVisible(true);
                }}
                type="link"
              >
                筛选数据
              </XlbButton>
            </XlbBasicForm.Item>
          </XlbBasicForm>

          <div className={styles.conditionGroup}>
            {formatCondition(conditionDataList)?.map((item: any, index: number) => {
              const isValidity = item.conditions[0].condition.treeList.some((s: any) => {
                const obj = s.children[0];
                const x = obj?.xField && Object.keys(obj.xField).length && obj.xField?.value;
                const judge = obj?.judgeField && Object.keys(obj.judgeField).length && obj.judgeField?.value;
                return x && judge;
              });
              if (!isValidity) return null;
              return (
                <Fragment key={index}>
                  {index > 0 && <div className={styles.or}>或</div>}
                  <div className={styles.conditionList}>
                    {item.conditions[0].condition.treeList.map((c: any, i: number) => {
                      const temp = c.children[0];
                      const x = temp?.xField && Object.keys(temp.xField).length && temp.xField?.value;
                      const judge = temp?.judgeField && Object.keys(temp.judgeField).length && temp.judgeField?.value;
                      if (!(x && judge)) return null;
                      return (
                        <div key={i} className={styles.condition}>
                          <div className={styles.name}>{c.children[0].xField.label}</div>
                          <div className={styles.judge}>
                            {judgeListEnum.find((f) => f.value === c.children[0].judgeField.value)?.label}
                          </div>
                          <div className={styles.value}>{c.children[0].yField.value}</div>
                        </div>
                      );
                    })}
                  </div>
                </Fragment>
              );
            })}
          </div>
        </div>
      </div>
      <ConditionModal
        visible={visible}
        onCancel={() => setVisible(false)}
        defaultValue={conditionDataList}
        setDefaultValue={setConditionDataList}
      />
    </>
  );
};
export default forwardRef(OtherSetting);
