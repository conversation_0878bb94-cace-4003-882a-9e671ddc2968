import React, { useState, useEffect, Fragment } from 'react'
import { Modal, Input, Radio, Space, Button, Checkbox, message, Select, Form } from 'antd'
import style from './batchChange.less'
// import XlbGoodsTypeModal from '@/components/common/xlbGoodsTypeModal'
import { batch } from '../../data'
import Api  from '../../service'
import { XlbTipsModal,XlbBasicData,XlbUploadFile,XlbIcon as IconFont } from '@xlb/components'
const { Option } = Select
import { LStorage } from '@/utils/storage'
import { hasAuth } from '@/utils'

const BatchChange = (props: any) => {
  const userInfo = LStorage.get('userInfo')
  const { visible, handleCancel, getData } = props
  const [goodsTypeModalVisible, setGoodsTypeModalVisible] = useState(false)
  const [loading, setLoading] = useState(false)
  const [uploadFileModalVisible, setUploadFileModalVisible] = useState(false)
  const [uploadFileType, setUploadFileType] = useState('门店')
  const [ErrorModalVisible, setErrorModalVisible] = useState<boolean>(false)
  const [selectedObj, setSelectedObj] = useState<object>([]) // 已选择的
  const [form] = Form.useForm()
  const [storeModal, setStoreModal] = useState<any>({
    modalVisible: false, // 弹窗是否展示
    storeItem: {}, // 接收弹窗选中的值
    modalState: {
      isMultiple: true,
      modalType: 'item'
    }
  })
  const [ErrorState, setErrorState] = useState<any>({
    isCancel: true,
    isConfirm: true,
    msg: '',
    fail_list: []
  })
  const [TipModalVisible, setTipModalVisible] = useState<boolean>(false)
  const [stateTips, setStateTips] = useState<{
    tips: string
    isConfirm: boolean
    isCancel: boolean
    showDesc: string
  }>({
    tips: '',
    isConfirm: true,
    isCancel: false,
    showDesc: ''
  })
  const handleOk = async () => {
    // console.log(form.getFieldsValue());
    if (form.getFieldValue('store_names') === undefined) {
      setStateTips({
        tips: `请先选择门店`,
        isConfirm: true,
        isCancel: false,
        showDesc: 'submit'
      })
      setTipModalVisible(true)
      return
    }
    if (form.getFieldValue('modify_scope') === undefined) {
      setStateTips({
        tips: `请先选择商品范围`,
        isConfirm: true,
        isCancel: false,
        showDesc: 'submit'
      })
      setTipModalVisible(true)
      return
    }
    if (form.getFieldValue('modify_scope') === 1 && !form.getFieldValue('category_names')) {
      setStateTips({
        tips: `请先选择商品类别`,
        isConfirm: true,
        isCancel: false,
        showDesc: 'submit'
      })
      setTipModalVisible(true)
      return
    }
    if (form.getFieldValue('modify_scope') === 2 && !form.getFieldValue('item_names')) {
      setStateTips({
        tips: `请先选择商品档案`,
        isConfirm: true,
        isCancel: false,
        showDesc: 'submit'
      })
      setTipModalVisible(true)
      return
    }
    if (!form.getFieldValue('checkValue')?.length) {
      setStateTips({
        tips: `请先选择修改内容`,
        isConfirm: true,
        isCancel: false,
        showDesc: 'submit'
      })
      setTipModalVisible(true)
      return
    }
    const data = { ...form.getFieldsValue() }
    if (data['store_names']) {
      data['store_ids'] = form.getFieldValue('store_ids')
    }
    batch.forEach((item: any) => {
      // 筛选勾选项并赋值
      if (form.getFieldValue('checkValue')?.includes(item.value)) {
        data[item.value] = form.getFieldValue(item.value)
      } else {
        data[item.value] = undefined
      }
    })
    const modify_scope = form.getFieldValue('modify_scope')
    if (modify_scope === 1) {
      data['item_category_ids'] = form.getFieldValue('category_names')
        ? form.getFieldValue('category_ids')
        : []
    } else if (modify_scope === 2) {
      data['item_ids'] = form.getFieldValue('item_names') ? form.getFieldValue('item_ids') : []
    }
    setLoading(true)
    const res = await Api.batchUpdate(data)
    setLoading(false)
    if (res.code === 0) {
      message.success('操作成功')
      handleCancel()
      getData(1)
      form.resetFields()
    }
  }
  // 商品选择确认
  const goodsSubmit = (list: any) => {
    if (storeModal.modalState.modalType === 'item') {
      form.setFieldsValue({
        item_ids: list.map((v: any) => v.id),
        item_names: list.map((v: any) => v.name).join(',')
      })
    } else if (storeModal.modalState.modalType === 'store') {
      if (list.length == 0) {
        form.setFieldsValue({
          store_names: LStorage.get('userInfo').store_name,
          store_ids: [LStorage.get('userInfo').store_id]
        })
      } else {
        form.setFieldsValue({
          store_names: list.map((v: any) => v.store_name).join(','),
          store_ids: list.map((v: any) => v.id)
        })
      }
    }
    setStoreModal({ ...storeModal, modalVisible: false })
  }
  // 商品分类选择确认
  const selectSubmit = (obj: any) => {
    setSelectedObj(obj)
    form.setFieldsValue({
      category_ids: obj.map((v: any) => v.id),
      category_names: obj.map((v: any) => v.name).join(',')
    })
    setGoodsTypeModalVisible(false)
  }
  const renderCheckBox = (item: string) => {
    return (
      <Select size="small" disabled={!hasAuth(userInfo, [`门店商品属性/${item}`, '编辑'])}>
        <Option value={0}> </Option>
        <Option value={2}>是</Option>
        <Option value={1}>否</Option>
      </Select>
    )
  }

  //导入
  const importItem = (e: any) => {

    if (e?.state) {
      if(uploadFileType == '商品'){
        form.setFieldsValue({
          item_ids: e.items.map((v: any) => v.id),
          item_names: e.items.map((v: any) => v.name).join(',')
        })
      }else {
        console.log("e",e)
        form.setFieldsValue({
          store_ids: e?.store_ids?.map((v: any) => v),
          store_names: e?.store_names?.map((v: any) => v).join(',')
        })
      }
      setUploadFileModalVisible(false)
    } else {
      setErrorModalVisible(true)
      setErrorState({
        ...ErrorState,
        isConfirm: false,
        fail_list: e.error_messages
      })
    }
  }
  useEffect(() => {
    form.setFieldsValue({
      store_names:
        LStorage.get('userInfo')?.query_stores &&
        LStorage.get('userInfo')?.query_stores.length === 1
          ? LStorage.get('userInfo')?.query_stores[0]?.store_name
          : undefined,
      store_ids:
        LStorage.get('userInfo')?.query_stores &&
        LStorage.get('userInfo')?.query_stores.length === 1
          ? [LStorage.get('userInfo')?.query_stores[0]?.id]
          : undefined
    })
    setSelectedObj([]) //清空弹窗中已选择的数据
  }, [visible])

  return (
    // <Modal
    //   title={'批量修改'}
    //   style={{ top: -50 }}
    //   centered
    //   visible={visible}
    //   maskClosable={false}
    //   onOk={handleOk}
    //   onCancel={() => {
    //     handleCancel()
    //     form.resetFields()
    //   }}
    //   width={900}
    //   confirmLoading={loading}
    // >
    //   <Form form={form}>
    //     <div className={style.box}>
    //       <p className={style.title}>修改门店</p>
    //       <Space direction="vertical" style={{ marginLeft: 61, marginTop: 5 }}>
    //         门店
    //       </Space>
    //       <Form.Item name="store_names">
    //         <Input
    //           readOnly
    //           size="small"
    //           suffix={<IconFont name="sousuo" color= '#666666'  />}
    //           onClick={() => {
    //             setStoreModal({
    //               ...storeModal,
    //               modalVisible: true,
    //               modalState: {
    //                 isMultiple: true,
    //                 modalType: 'store',
    //                 hasChosenItem: form.getFieldValue('store_names')
    //                   ? form.getFieldValue('store_ids').join(',')
    //                   : ''
    //               }
    //             })
    //           }}
    //         />
    //       </Form.Item>
    //       <Button
    //                 type="primary"
    //                 size="small"
    //                 style={{ marginLeft: -25 }}
    //                 onClick={() => {
    //                   setUploadFileModalVisible(true),setUploadFileType('门店')
    //                 }}
    //               >
    //                 导入
    //               </Button>
    //     </div>
    //     <div className={style.box}>
    //       <p className={style.title}>商品范围</p>
    //       <Form.Item name="modify_scope">
    //         <Radio.Group>
    //           <Space direction="vertical">
    //             <Radio value={0}>全部商品</Radio>
    //             <Radio value={1}>
    //               商品类别
    //               <Form.Item name="category_names">
    //                 <Input
    //                   readOnly
    //                   size="small"
    //                   suffix={<SearchOutlined style={{ color: '#666666' }} />}
    //                   onClick={() => setGoodsTypeModalVisible(true)}
    //                 />
    //               </Form.Item>
    //             </Radio>
    //             <Radio value={2}>
    //               商品档案
    //               <Form.Item name="item_names">
    //                 <Input
    //                   readOnly
    //                   size="small"
    //                   suffix={<SearchOutlined style={{ color: '#666666' }} />}
    //                   onClick={() =>
    //                     setStoreModal({
    //                       ...storeModal,
    //                       modalVisible: true,
    //                       modalState: {
    //                         isMultiple: true,
    //                         modalType: 'item',
    //                         hasChosenItem: form.getFieldValue('item_names')
    //                           ? form.getFieldValue('item_ids').join(',')
    //                           : ''
    //                       }
    //                     })
    //                   }
    //                 />
    //               </Form.Item>
    //               <Button
    //                 type="primary"
    //                 size="small"
    //                 style={{ marginLeft: -25 }}
    //                 onClick={() => {
    //                   setUploadFileModalVisible(true),setUploadFileType('商品')
    //                 }}
    //               >
    //                 导入
    //               </Button>
    //             </Radio>
    //           </Space>
    //         </Radio.Group>
    //       </Form.Item>
    //     </div>
    //     <div className={style.box} style={{ marginBottom: 0 }}>
    //       <p className={style.title}>修改内容</p>
    //       <Form.Item name="checkValue">
    //         <Checkbox.Group style={{ width: '100%' }}>
    //           {batch.map((item) => {
    //             return (
    //               <Fragment key={item.value}>
    //                 <Checkbox
    //                   value={item.value}
    //                   disabled={!hasAuth(userInfo, [`门店商品属性/${item.label}`, '编辑'])}
    //                 >
    //                   {item.label}
    //                 </Checkbox>
    //                 <Form.Item name={item.value} key={item.value}>
    //                   {renderCheckBox(item.label)}
    //                 </Form.Item>
    //               </Fragment>
    //             )
    //           })}
    //         </Checkbox.Group>
    //       </Form.Item>
    //     </div>
    //   </Form>
    //   <XlbBasicData
    //     visible={storeModal.modalVisible} //是否展示
    //     handleOk={goodsSubmit} // 确定
    //     handleCancel={() => setStoreModal({ ...storeModal, modalVisible: false })} //取消
    //     modalState={storeModal.modalState} // 弹窗配置参数
    //   />
    //   <XlbGoodsTypeModal
    //     isModalVisible={goodsTypeModalVisible} //显隐
    //     setIsModalVisible={setGoodsTypeModalVisible} //控制显隐
    //     selectSubmit={selectSubmit} //确认
    //     selectedObj={selectedObj} //已选数据
    //   />
    //   <XlbTipsModal
    //     visible={TipModalVisible}
    //     setTipModal={setTipModalVisible}
    //     handConfirm={() => setTipModalVisible(false)}
    //     state={stateTips}
    //     setTipState={setStateTips}
    //   />
    //   <XlbUploadFile
    //     handleConfirm={importItem}
    //     visible={uploadFileModalVisible}
    //     templateUrl={uploadFileType == '商品' ? '/erp-mdm/hxl.erp.item.shorttemplate.download':'/erp-mdm/hxl.erp.storenametemplate.download'}
    //     importUrl={uploadFileType == '商品' ? '/erp/hxl.erp.items.batchimport':'/erp/hxl.erp.storename.import'}
    //     handleCancel={() => setUploadFileModalVisible(false)}
    //   />
    //   <XlbErrorModal
    //     visible={ErrorModalVisible}
    //     setErrorModal={setErrorModalVisible}
    //     handleConfirm={() => setErrorModalVisible(false)}
    //     state={ErrorState}
    //     setErrorState={setErrorState}
    //   />
    // </Modal>
    <></>
  )
}
export default BatchChange
