import styles from './approvalDetail.module.less';
import { XlbTable, XlbBaseUpload, XlbIcon as IconFont } from '@xlb/components';
import isEmpty from 'lodash/isEmpty';
import { getFileExtension, getShowName, getTextWidth, getVisibleHandle, findFieldId } from '../utils';
import { isArray, isObject } from 'lodash';
const ApprovalDetail = ({ detailData }: any) => {
  const renderTable = (data: any, children: any) => {
    if (isEmpty(data) || isEmpty(children)) {
      return <div>-</div>;
    }

    const columns = children?.map((item: any) => {
      if (item.type === 'IMAGE_FILE' || item.type === 'DOCUMENT_FILE' || item.type === 'VIDEO_FILE') {
        return {
          code: item?.business_field,
          name: item?.name,
          features: { sortable: true },
          width: 300,
          render: (text: any) => {
            if (isEmpty(text)) {
              return;
            }
            const filterData = text.filter(Boolean);
            return (
              <XlbBaseUpload
                mode="look"
                buttonModal={false}
                showUpload={false}
                listType={'text'}
                fileList={filterData}
              />
            );
          },
        };
      } else {
        return {
          code: item?.business_field,
          name: item?.name,
          features: { sortable: true },
          // width: columnWidth
          width: getTextWidth(item?.name, '14px PingFangSC ') + 30,
        };
      }
    });
    columns.unshift({
      name: '序号',
      code: '_index',
      width: 60,
      align: 'center',
    });
    const dataSource = data;
    return (
      <XlbTable
        style={{ width: '100%', minHeight: 300, borderBottom: '1px #e5e6ea solid' }}
        dataSource={dataSource || []}
        columns={columns}
        total={data?.length}
        hideOnSinglePage
      />
    );
  };

  const renderDept = (val: any) => {
    if (!isEmpty(val)) {
      return val?.map((item: any, index: number) => {
        return (
          <div key={index} className={styles.APPendingAvatar}>
            <div>
              <IconFont name={'jiegou'} size={20} />
            </div>
            <div className={styles.name}>{item}</div>
          </div>
        );
      });
    }
    return <div>-</div>;
  };
  const renderPosition = (val: string | Record<string, any>) => {
    if (isEmpty(val)) {
      return '-';
    }
    if (isObject(val)) {
      return val?.address;
    } else {
      return val;
    }
  };
  const renderMember = (val: any) => {
    if (!isEmpty(val)) {
      return val.map((item: any, index: number) => {
        const name = getShowName(item);
        return (
          <div key={index} className={styles.APPendingAvatar}>
            <div style={{ backgroundColor: '#3D66FE' }} className={styles.APPendingAvatarImg}>
              {name}
            </div>

            <div className={styles.name}>{name}</div>
          </div>
        );
      });
    }
    return <div>-</div>;
  };
  const renderFile = (fileData: any) => {
    if (isEmpty(fileData) || !isArray(fileData)) {
      return <div>-</div>;
    }
    const formatFileData = fileData?.filter(Boolean)?.map((item: any) => {
      if (isObject(item)) {
        if (!Reflect.has(item, 'suffix_type')) {
          return {
            ...item,
            id: item,
            suffix_type: getFileExtension((item as any)?.url),
          };
        } else {
          return { ...item, id: item };
        }
      } else {
        return {
          name: item,
          suffix_type: getFileExtension(item),
          url: item,
          id: item,
        };
      }
    });
    return (
      <XlbBaseUpload mode="look" buttonModal={false} showUpload={false} listType={'text'} fileList={formatFileData} />
    );
  };

  const renderParagraph = (dataParagraph: any, variables: any) => {
    if (isEmpty(dataParagraph)) {
      return '-';
    }

    const resultData = {
      content: dataParagraph,
      variables,
    };

    return renderDetailByData(resultData);
  };
  const renderDetailByData = (data: any) => {
    const { isNeedFilter } = detailData;
    const { content, variables } = data;
    const filterContent = content?.filter((item: any) => item.readable);

    let listData = [];
    if (isNeedFilter) {
      listData = filterContent?.filter((item: any) => {
        if (item?.type === 'PARAGRAPH') {
          // 将paragraph中不存在于variables的都过滤掉
          if (isEmpty(item?.children)) {
            return false;
          }
          item.children = item?.children.filter((item2: any) => {
            return variables[item2.business_field];
          });
          return !!item?.children?.length;
        } else {
          return variables[item.business_field];
        }
      });
    } else {
      listData = filterContent;
    }

    // 临时存储计算公式、条件公式结果
    let formulaObj: any = {};
    const computation = (formulas: any) => {
      const formulaStrList = formulas.map((i: any) => {
        if (i.tag_type === 'symbol') {
          return i.name;
        }
        if (i.tag_type === 'control') {
          return i.value;
        }
        if (i.tag_type === 'field') {
          if (i.type === 'CALCULATION_FORMULA') {
            const { targetItem } = findFieldId(detailData.content, i.id);
            if (targetItem && targetItem?.calculation_formula) {
              const final = computation(targetItem?.calculation_formula);
              return final < 0 ? `(${final})` : final;
            }
            return 0;
          }
          if (i.type === 'CONDITIONAL_FORMULA') {
            const { targetItem } = findFieldId(detailData.content, i.id);
            const field = targetItem.business_field;
            if (field && variables[field]) {
              const final = variables[field];
              return final < 0 ? `(${final})` : final;
            }
            return 0;
          }
          const { fieldId } = findFieldId(detailData.content, i.id);
          if (!fieldId || !detailData.variables.hasOwnProperty(fieldId)) {
            return 0;
          }
          const final = detailData.variables[fieldId];
          return parseInt(final) < 0 ? `(${final})` : final;
        }
      });
      let result = 0;
      if (formulaStrList.length !== 0) {
        const formulaStr = formulaStrList.join('');
        console.log('计算公式过程：', formulaStr);
        result = eval(formulaStr);
      }
      return result;
    };
    return listData?.map((item: any, index: number) => {
      const type = item?.type;
      const name = item?.name;
      const showFlag = getVisibleHandle(item?.condition_group, variables);
      if (!showFlag) {
        return;
      }

      //空数据统一用 “-” 占位
      if (
        type !== 'PARAGRAPH' &&
        type !== 'TABLE' &&
        isEmpty(variables[item.business_field]) &&
        !variables[item.business_field] &&
        variables[item.business_field] !== 0 &&
        item.type !== 'CALCULATION_FORMULA' &&
        item.type !== 'CONDITIONAL_FORMULA'
      ) {
        return (
          <div key={index} className={styles.baseStyle}>
            <div className={styles.label}>{item.name}</div>
            <div>-</div>
          </div>
        );
      }

      if (type === 'CALCULATION_FORMULA') {
        const resultNumber = computation(item.calculation_formula);
        formulaObj[item.business_field] = resultNumber;
        return (
          <div key={index} className={styles.baseStyle}>
            <div className={styles.label}>{item.name}</div>
            <div>{resultNumber}</div>
          </div>
        );
      }

      if (type === 'CONDITIONAL_FORMULA') {
        let conditionValue = '-';
        item.conditional_formula.forEach((condition: any) => {
          const groups = condition.formula_condition_group;
          if (groups.length !== 0) {
            const showFlag = getVisibleHandle(groups, variables);
            console.log('是否满足条件公式：', showFlag, condition.value);
            if (showFlag) {
              conditionValue = condition.value;
            }
          }
        });
        if (conditionValue !== '-') {
          variables[item.business_field] = conditionValue;
          formulaObj[item.business_field] = conditionValue;
        }

        return (
          <div key={index} className={styles.baseStyle}>
            <div className={styles.label}>{item.name}</div>
            <div>{conditionValue}</div>
          </div>
        );
      }
      if (type === 'DESCRIBE') {
        return (
          <div key={index} className={styles.baseStyle}>
            <div style={{ background: ' #F7F8FA', borderRadius: 6, padding: '8px 12px', width: 400 }}>
              {variables[item.business_field]}
            </div>
          </div>
        );
      }

      if (type === 'NUMBER') {
        let num_show = variables[item.business_field];
        if (name === '数字' && item.content && num_show) {
          const range = JSON.parse(item.content);
          const { precision } = range;
          if (precision) num_show = parseFloat(num_show.toFixed(precision));
        }
        return (
          !isObject(variables[item.business_field]) && (
            <div key={index} className={styles.baseStyle}>
              <div className={styles.label}>{item.name}</div>
              <div>{num_show}</div>
            </div>
          )
        );
      }

      if (type === 'SINGLE_LINE_TEXT' || type === 'TEL' || type === 'DATE' || type === 'MULTI_LINE_TEXT') {
        return (
          !isObject(variables[item.business_field]) && (
            <div key={index} className={styles.baseStyle}>
              <div className={styles.label}>{item.name}</div>
              <div>{variables[item.business_field]}</div>
            </div>
          )
        );
      }
      if (type === 'AMOUNT') {
        return (
          <div key={index} className={styles.baseStyle}>
            <div className={styles.label}>{item.name}</div>
            <div style={{ color: '#3D66FE' }}>{variables[item.business_field]}</div>
          </div>
        );
      }
      if (type === 'MULTIPLE_CHOICE') {
        return (
          <div key={index} className={styles.baseStyle}>
            <div className={styles.label}>{item.name}</div>
            <div>
              {isArray(variables[item.business_field])
                ? variables[item.business_field]?.join('、')
                : variables[item.business_field]}
            </div>
          </div>
        );
      }
      if (type === 'DATE_RANGE') {
        return (
          <div key={index} className={styles.baseStyle}>
            <div className={styles.label}>{item.name}</div>
            <div>{variables[item.business_field]?.join('-')}</div>
          </div>
        );
      }
      if (type === 'SINGLE_CHOICE') {
        return (
          <div key={index} className={styles.baseStyle}>
            <div className={styles.label}>{item.name}</div>
            <div>{variables[item.business_field]}</div>
          </div>
        );
      }
      if (type === 'DEPT') {
        return (
          <div key={index} className={styles.baseStyle}>
            <div className={styles.label}>{item.name}</div>
            <div className={styles.memberValue}>{renderDept(variables[item.business_field])}</div>
          </div>
        );
      }
      if (type === 'MEMBER') {
        return (
          <div key={index} className={styles.baseStyle}>
            <div className={styles.label}>{item.name}</div>
            <div className={styles.memberValue}>{renderMember(variables[item.business_field])}</div>
          </div>
        );
      }
      if (type === 'POSITION') {
        return (
          <div key={index} className={styles.baseStyle}>
            <div className={styles.label}>{item.name}</div>
            <div>{renderPosition(variables[item.business_field])}</div>
          </div>
        );
      }
      if (type === 'ADDRESS') {
        return (
          <div key={index} className={styles.baseStyle}>
            <div className={styles.label}>{item.name}</div>
            <div>{variables[item.business_field] ? variables[item.business_field]['name'] : ''}</div>
          </div>
        );
      }
      if (type === 'IMAGE_FILE' || type === 'DOCUMENT_FILE' || type === 'VIDEO_FILE') {
        return (
          <div key={index} className={styles.baseStyle}>
            <div className={styles.label}>{item.name}</div>
            <div>{renderFile(variables[item.business_field])}</div>
          </div>
        );
      }

      if (type === 'TABLE') {
        return (
          <div key={index} className={styles.tableStyle}>
            <div className={styles.paragraph}>
              <div className={styles.circle}></div>
              <div className={styles.name}>{item.name}</div>
            </div>
            <div className={styles.tableItem}>{renderTable(variables[item.business_field], item?.children)}</div>
          </div>
        );
      }
      if (type === 'PARAGRAPH') {
        return (
          <div key={index} className={styles.tableStyle}>
            <div className={styles.paragraph}>
              <div className={styles.circle}></div>
              <div className={styles.name}>{item.name}</div>
            </div>
            <div className={styles.paragraphItem}>{renderParagraph(item?.children, variables)}</div>
          </div>
        );
      }

      return;
    });
  };
  return (
    <div className={styles.approvalContainer}>
      <div className={styles.detailApproval}>
        <div className={styles.detailLine}>|</div>
        <p id="testaaa" className={styles.detailText}>
          <span>审批详情</span>
        </p>
      </div>

      <div>{renderDetailByData(detailData)}</div>
    </div>
  );
};
export default ApprovalDetail;
