import {
  goodsType,
  buyLimits,
  countType,
  makeDate,
  rateList,
  dayOrMohth,
  inCheckList,
  dayOrRadio,
  checkOptions,
  purchaseType,
  itemStatusType,
  statusTag,
  temperatureType,
} from './data';
import { ArtColumn } from 'ali-react-table';
import { hasAuth } from '@/utils';
import { COLUMN_WIDTH_ESUM } from '@/constants/common';
import { SearchFormType } from '@xlb/components';
import { FormInstance, message } from 'antd';

export const warmList = Object.freeze([
  { label: '常温', value: 0 },
  { label: '冷藏', value: 1 },
  { label: '冷冻', value: 2 },
  { label: '生鲜', value: 3 },
]);
const titleArr = [
  { label: '基本信息', value: 'baseInfo' },
  { label: '计量单位', value: 'unitCount' },
  { label: '价格设置（价格均按基本单位进行设置）', value: 'priceSetting' },
  { label: '体积重量', value: 'volumeWeight' },
  { label: '其他设置', value: 'other' },
];
const goodsInfoArr: SearchFormType[] = [
  {
    label: '商品代码',
    value: 'code',
    clear: true,
    rules: [
      { required: true, message: '请输入商品代码' },
      { pattern: /^[A-Za-z0-9]+$/, message: '支持30位数字和字母' },
    ],
  },
  {
    label: '商品条码',
    value: 'bar_code',
    clear: true,
    rules: [
      { required: true, message: '请输入商品条码' },
      { pattern: /^[A-Za-z0-9-]+$/, message: '支持30位数字和字母' },
    ],
  },
  { label: '商品名称', value: 'name', width: 410, clear: true, rules: [{ required: true, message: '请输入商品名称' }] },
  { label: '速记码', value: 'shorthand_code', clear: true, rules: [{ required: true, message: '请输入速记码' }] },
  { label: '零售规格', value: 'retail_spec', clear: true },
  { label: '采购规格', value: 'purchase_spec', clear: true, rules: [{ required: true, message: '请输入采购规格' }] },
  {
    label: '商品分类',
    value: 'category_names',
    type: 'dialog',
    clear: true,
    ckeck: true,
    rules: [{ required: true, message: '请选择商品分类' }],
  },
  {
    label: '核算方式',
    value: 'account_method',
    type: 'select',
    initialValue: '移动加权平均',
    options: countType,
    rules: [{ required: true, message: '请选择核算方式' }],
  },
  {
    label: '商品部门',
    value: 'item_dept_id',
    type: 'select',
    options: [],
    showSearch: true,
    // rules: [{ required: true, message: '请选择商品部门' }]
  },
  {
    label: '采购范围',
    value: 'purchase_scope',
    type: 'select',
    options: buyLimits,
    rules: [{ required: true, message: '请输入采购范围' }],
  },
  {
    label: '采购类型',
    value: 'purchase_type',
    type: 'select',
    hidden: true,
    options: purchaseType,
    rules: [{ required: true, message: '请选择采购类型' }],
  },
  {
    label: '商品类型',
    value: 'item_type',
    type: 'select',
    options: goodsType,
    initialValue: 'STANDARD',
    rules: [{ required: true, message: '请选择商品类型' }],
  },
  {
    label: '商品状态',
    value: 'item_status',
    type: 'select',
    options: itemStatusType,
    rules: [{ required: true, message: '请选择商品状态' }],
  },
  {
    label: '状态标签',
    value: 'item_status_tag',
    type: 'select',
    hidden: true,
    options: statusTag,
    rules: [{ required: true, message: '请选择状态标签' }],
  },
  {
    label: '商品品牌',
    value: 'item_brand_id',
    type: 'select',
    options: [],
    showSearch: true,
    rules: [{ required: true, message: '请选择商品品牌' }],
  },
  {
    label: '商品产地',
    value: 'origin_place',
    clear: true,
  },
  {
    label: '温层',
    value: 'temperature',
    clear: false,
    type: 'select',
    options: temperatureType,
    rules: [{ required: true, message: '请选择温层' }],
  },
  {
    label: '系统编码',
    value: 'id',
    clear: true,
    disabled: true,
  },
  {
    label: '乐檬商品编码',
    value: 'ama_id',
    clear: true,
    disabled: true,
  },
  {
    label: '玩具序列号',
    value: 'toy_code',
    clear: true,
    // rules: [{ pattern: /^[A-Za-z0-9]+$/, message: '支持30位数字和字母' }]
  },
  {
    label: '执行标准',
    value: 'executive_standard',
    type: 'input',
    clear: true,
    // rules: [{ required: true, message: '请填写执行标准' }],
    ckeck: true,
  },
  {
    label: '斤规格',
    type: 'hasChildTwo',
    // required: true,
    children: [
      {
        value: 'weight_spec',
        type: 'input',
        width: 140,
        rules: [
          // { required: true, message: '请填写斤规格' },
          { pattern: /^(?:0|[1-9]\d{0,4}|99999)$/, message: '请输入0-99999之间的数字' },
        ],
      },
      {
        value: 'weight_spec_unit',
        width: 140,
        type: 'select',
        options: [],
        showSearch: true,
        // rules: [
        //   { required: true, message: '请选择斤规格单位' }
        // ],
        // initialValue: '个'
      },
    ],
  },
  {
    label: '税收分类编码',
    value: 'tax_no',
    type: 'input',
    clear: true,
    ckeck: true,
    rules: [{ pattern: /^\d{1,19}$/, message: '仅支持输入数字,且不能超过19位' }],
    // rules: [
    //   { required: true, message: '请填写税收分类编码' }
    // ],
  },
  {
    label: '乐檬经营范围',
    value: 'item_managements_names',
    type: 'dialog',
    clear: true,
    ckeck: true,
    disabled: true,
    // rules: [
    //   { required: true, message: '请填写税收分类编码' }
    // ],
  },
  {
    label: '采购经营范围',
    value: 'business_scope_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'itemBusinessscope',
      isMultiple: true,
      data: { business_type: '0', business_scope_type: 0 },
      idsKey: 'business_scope_ids',
    },
    allowClear: true,
    colon: true,
  },
  {
    label: '销售名称',
    value: 'sale_name',
    width: 410,
    type: 'input',
    rules: [
      { required: true, message: '请填写销售名称' },
      { pattern: /^(?!.*')[^']*$/, message: '请输入中文单引号' },
    ],
    clear: true,
    ckeck: true,
  },
  {
    label: '进口商品',
    value: 'imports',
    type: 'select',
    clear: false,
    ckeck: true,
    options: [
      { label: '是', value: true },
      { label: '否', value: false },
    ],
    rules: [{ required: true, message: '请选择进口商品' }],
    initialValue: false,
  },
  // {
  //   label: '金蝶商品编码',
  //   value: 'kingdee_code',
  //   type: 'input',
  //   clear: true,
  //   ckeck: true,
  //   rules: [{ pattern: /^[A-Za-z0-9]+$/, message: '支持30位数字和字母' }]
  // }
];
const unitCountArr = [
  {
    label: '基本单位',
    value: 'unit',
    type: 'select',
    options: [],
    showSearch: true,
    initialValue: '公斤',
    isWrap: true,
    disabled: !hasAuth(['商品档案/单位和换算率', '编辑']),
    rules: [{ required: true, message: '请选择基本单位' }],
  },
  {
    label: '库存单位',
    value: 'stock_unit',
    type: 'select',
    initialValue: '公斤',
    showSearch: true,
    options: [],
    disabled: !hasAuth(['商品档案/单位和换算率', '编辑']),
    rules: [{ required: true, message: '请选择库存单位' }],
  },
  {
    label: '库存换算率',
    value: 'stock_ratio',
    clear: true,
    rules: [
      { required: true, message: '请填入换算率' },
      { pattern: /^[1-9][0-9]*(\.[0-9]+|[0-9]*)$/, message: '请输入正确格式的数据' },
    ],
    disabled: !hasAuth(['商品档案/单位和换算率', '编辑']),
    initialValue: Number(1).toFixed(3),
  },
  {
    label: '采购单位',
    value: 'purchase_unit',
    type: 'select',
    initialValue: '公斤',
    showSearch: true,
    options: [],
    disabled: !hasAuth(['商品档案/单位和换算率', '编辑']),
    rules: [{ required: true, message: '请选择采购单位' }],
  },
  {
    label: '采购换算率',
    value: 'purchase_ratio',
    clear: true,
    rules: [
      { required: true, message: '请填入换算率' },
      { pattern: /^[1-9][0-9]*(\.[0-9]+|[0-9]*)$/, message: '请输入正确格式的数据' },
    ],
    disabled: !hasAuth(['商品档案/单位和换算率', '编辑']),
    initialValue: Number(1).toFixed(3),
  },
  {
    label: '批发单位',
    value: 'wholesale_unit',
    type: 'select',
    initialValue: '公斤',
    showSearch: true,
    options: [],
    disabled: !hasAuth(['商品档案/单位和换算率', '编辑']),
    rules: [{ required: true, message: '请选择批发单位' }],
  },
  {
    label: '批发换算率',
    value: 'wholesale_ratio',
    clear: true,
    rules: [
      { required: true, message: '请填入换算率' },
      { pattern: /^[1-9][0-9]*(\.[0-9]+|[0-9]*)$/, message: '请输入正确格式的数据' },
    ],
    disabled: !hasAuth(['商品档案/单位和换算率', '编辑']),
    initialValue: Number(1).toFixed(3),
  },
  {
    label: '配送单位',
    value: 'delivery_unit',
    type: 'select',
    options: [],
    showSearch: true,
    initialValue: '公斤',
    disabled: !hasAuth(['商品档案/单位和换算率', '编辑']),
    rules: [{ required: true, message: '请选择配送单位' }],
  },
  {
    label: '配送换算率',
    value: 'delivery_ratio',
    clear: true,
    rules: [
      { required: true, message: '请填入换算率' },
      { pattern: /^[1-9][0-9]*(\.[0-9]+|[0-9]*)$/, message: '请输入正确格式的数据' },
    ],
    disabled: !hasAuth(['商品档案/单位和换算率', '编辑']),
    initialValue: Number(1).toFixed(3),
  },
];
const volumeWeightArr = [
  {
    label: '采购单位:长(cm)',
    value: 'length',
    clear: true,
    initialValue: Number(0).toFixed(2),
    required: true,
    rules: [
      { required: true, message: '请输入数据' },
      { pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' },
      {
        pattern: /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
        message: '不能大于1000cm且保留两位小数',
      },
      {
        validator: (_: any, value: any) => {
          if (value <= 0) {
            console.log('value', value);
            return Promise.reject('请输入正数');
          }

          return Promise.resolve();
        },
      },
    ],
  },
  {
    label: '宽(cm)',
    value: 'width',
    clear: true,
    initialValue: Number(0).toFixed(2),
    rules: [
      { required: true, message: '请输入数据' },
      { pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' },
      {
        pattern: /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
        message: '不能大于1000cm且保留两位小数',
      },
      {
        validator: (_: any, value: any) => {
          if (value <= 0) {
            console.log('value', value);
            return Promise.reject('请输入正数');
          }

          return Promise.resolve();
        },
      },
    ],
  },
  {
    label: '高(cm)',
    value: 'height',
    clear: true,
    wrapWidth: 500,
    initialValue: Number(0).toFixed(2),
    rules: [
      { required: true, message: '请输入数据' },
      { pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' },
      {
        pattern: /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
        message: '不能大于1000cm且保留两位小数',
      },
      {
        validator: (_: any, value: any) => {
          if (value <= 0) {
            console.log('value', value);
            return Promise.reject('请输入正数');
          }

          return Promise.resolve();
        },
      },
    ],
  },
  {
    label: '基本单位: 长(cm)',
    value: 'basic_length',
    clear: true,
    initialValue: Number(0).toFixed(2),
    rules: [
      { required: true, message: '请输入数据' },
      { pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' },
      {
        pattern: /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
        message: '不能大于1000cm且保留两位小数',
      },
      {
        validator: (_: any, value: any) => {
          if (value <= 0) {
            return Promise.reject('请输入正数');
          }
          return Promise.resolve();
        },
      },
    ],
  },
  {
    label: '宽(cm)',
    value: 'basic_width',
    clear: true,
    initialValue: Number(0).toFixed(2),
    rules: [
      { required: true, message: '请输入数据' },
      { pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' },
      {
        pattern: /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
        message: '不能大于1000cm且保留两位小数',
      },
      {
        validator: (_: any, value: any) => {
          if (value <= 0) {
            return Promise.reject('请输入正数');
          }

          return Promise.resolve();
        },
      },
    ],
  },
  {
    label: '高(cm)',
    value: 'basic_height',
    clear: true,
    wrapWidth: 500,
    initialValue: Number(0).toFixed(2),
    rules: [
      { required: true, message: '请输入数据' },
      { pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' },
      {
        pattern: /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
        message: '不能大于1000cm且保留两位小数',
      },
      {
        validator: (_: any, value: any) => {
          if (value <= 0) {
            return Promise.reject('请输入正数');
          }
          return Promise.resolve();
        },
      },
    ],
  },
  {
    label: '采购体积(m³)',
    value: 'purchase_volume',
    clear: true,
    disabled: true,
    initialValue: Number(0).toFixed(2),
    rules: [{ pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' }],
  },
  {
    label: '配送体积(m³)',
    value: 'delivery_volume',
    clear: true,
    disabled: true,
    initialValue: Number(0).toFixed(2),
    rules: [{ pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' }],
  },
  {
    label: '毛重(kg)',
    value: 'gross_weight',
    clear: true,
    initialValue: Number(0).toFixed(2),
    rules: [
      { required: true, message: '请输入毛重' },
      { pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' },
      {
        validator: async (_: any, value: any) => {
          console.log('value', value);
          if (value <= 0) {
            return Promise.reject('请输入正数');
          }
          return Promise.resolve();
        },
      },
    ],
  },
  {
    label: '净重(kg)',
    value: 'net_weight',
    clear: true,
    initialValue: Number(0).toFixed(2),
    rules: [
      { required: true, message: '请输入净重' },
      { pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' },
      {
        validator: (_: any, value: any) => {
          if (value <= 0) {
            return Promise.reject('请输入正数');
          }
          return Promise.resolve();
        },
      },
    ],
  },
];
const priceSetArr = [
  {
    title: '采购价',
    item: [
      {
        label: '采购价',
        value: 'purchase_price',
        rules: hasAuth(['商品档案/档案采购价', '查询'])
          ? [{ pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' }]
          : [],
        initialValue: Number(0).toFixed(2),
      },
    ],
  },
  { title: '配送价' },
  { title: '批发价' },
  {
    title: '默认零售价',
    item: [
      {
        label: '标准售价',
        value: 'sale_price',
        rules: hasAuth(['商品档案/档案零售价', '查询'])
          ? [{ pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' }]
          : [],
        initialValue: Number(0).toFixed(2),
      },
      {
        label: '会员价',
        value: 'sale_price_s',
        rules: hasAuth(['商品档案/档案零售价', '查询'])
          ? [{ pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' }]
          : [],
        initialValue: Number(0).toFixed(2),
      },
      {
        label: '售价3',
        value: 'sale_price_t',
        rules: hasAuth(['商品档案/档案零售价', '查询'])
          ? [{ pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' }]
          : [],
        initialValue: Number(0).toFixed(2),
      },
      {
        label: '售价4',
        value: 'sale_price_f',
        rules: hasAuth(['商品档案/档案零售价', '查询'])
          ? [{ pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' }]
          : [],
        initialValue: Number(0).toFixed(2),
      },
      {
        label: '最低售价',
        value: 'sale_min_price',
        rules: hasAuth(['商品档案/档案零售价', '查询'])
          ? [{ pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' }]
          : [],
        initialValue: Number(0).toFixed(2),
      },
      {
        label: '最高售价',
        value: 'sale_max_price',
        rules: hasAuth(['商品档案/档案零售价', '查询'])
          ? [{ pattern: /^\d+(\.{0,1}\d+){0,1}$/, message: '请输入正确格式的数据' }]
          : [],
        initialValue: Number(0).toFixed(2),
      },
    ],
  },
];

export const itemCheckOptions = [
  ...checkOptions.filter((v, i) => i > 0),
  { label: '翻牌商品', value: 'flipped_item' },
  { label: '确认商品', value: 'confirm_item' },
];
const otherArr = [
  {
    label: '保质期',
    type: 'hasChild',
    children: [
      { value: 'expire_type', type: 'select', width: 80, options: dayOrMohth, initialValue: 2, clear: false },
      {
        value: 'expire_type_num',
        width: 53,
        rules: [
          { required: true, message: '请输入数据' },
          { pattern: /^[0-9]+?$/, message: '请输入正确格式的数据' },
        ],
        initialValue: Number(1),
      },
    ],
  },
  {
    label: '收货规则',
    type: 'hasChild',
    children: [
      { value: 'receive_rule_type', type: 'select', width: 80, options: dayOrRadio, initialValue: 0, clear: false },
      {
        value: 'receive_rule_type_value',
        width: 53,
        rules: [
          { required: true, message: '请输入数据' },
          { pattern: /^[0-9]+?$/, message: '请输入正确格式的数据' },
        ],
        initialValue: Number(30),
      },
    ],
  },
  {
    label: '发货规则',
    type: 'hasChild',
    children: [
      {
        value: 'delivery_rule_type',
        type: 'select',
        width: 80,
        options: dayOrRadio,
        initialValue: 0,
        clear: false,
      },
      {
        value: 'delivery_rule_type_value',
        width: 53,
        dependencies: ['delivery_rule_type'],
        rules: [
          ({ getFieldValue }: FormInstance) => ({
            validator(_: any, value: string) {
              const receive_rule_type = getFieldValue('delivery_rule_type');
              const reg = /^[1-9]\d*$/;
              const reg1 = /^(0?[1-9]|[1-9][0-9])$/;
              if (!value) {
                return Promise.reject(new Error('请输入数据'));
              }
              if (value && receive_rule_type === 0 && !reg?.test(value)) {
                return Promise.reject(new Error('请输入大于0的正整数'));
              }
              if (value && receive_rule_type === 1 && !reg1?.test(value)) {
                return Promise.reject(new Error('请输入大于0小于100的正整数'));
              }
              return Promise.resolve();
            },
          }),
        ],
      },
    ],
  },
  {
    label: '中心临期提醒',
    type: 'hasChild',
    children: [
      { value: 'center_expire_type', type: 'select', width: 80, options: dayOrRadio, initialValue: 0, clear: false },
      {
        value: 'center_expire_type_value',
        width: 53,
        rules: [
          { required: true, message: '请输入数据' },
          { pattern: /^[0-9]+?$/, message: '请输入正确格式的数据' },
        ],
        initialValue: Number(0),
      },
    ],
  },
  {
    label: '门店临期提醒',
    type: 'hasChild',
    children: [
      { value: 'store_expire_type', type: 'select', width: 80, options: dayOrRadio, initialValue: 0, clear: false },
      {
        value: 'store_expire_type_value',
        width: 53,
        rules: [
          { required: true, message: '请输入数据' },
          { pattern: /^[0-9]+?$/, message: '请输入正确格式的数据' },
        ],
        initialValue: Number(0),
      },
    ],
  },
  {
    label: 'wms日期录入规则',
    value: 'date_in_type',
    type: 'select',
    options: makeDate,
    initialValue: 1,
    clear: false,
    rules: [{ required: true, message: '请选择' }],
  },
  {
    label: '进项税率',
    value: 'input_tax_rate',
    type: 'select',
    width: '130px',
    clear: true,
    options: [],
    initialValue: 13,
  },
  {
    label: '销项税率',
    value: 'output_tax_rate',
    type: 'select',
    width: '110px',
    clear: true,
    options: [],
    initialValue: 13,
  },
  {
    label: '入库抽检',
    value: 'in_check',
    type: 'select',
    width: '110px',
    clear: false,
    initialValue: 0,
    options: inCheckList,
  },
  // {
  //   label: '食品检测报告有效期',
  //   value: 'food_report_valid_date',
  //   type: 'datePicker'
  //   // rules: [{ pattern: /^(\d{1,2}|99)$/, message: '请输入正确格式的数据' }],
  //   // initialValue: Number(0).toFixed(0)
  // },
  // {
  //   label: '商品说明',
  //   value: 'item_desc',
  //   clear: true,
  //   width: 955
  // },
  {
    label: '商品属性',
    value: 'checkValue',
    type: 'checkGroup',
    options: [],
    defaultOptions: ['stop_purchase', 'stop_sale', 'stop_request', 'stop_wholesale', 'point', 'pos_bargain'],
  },
  { label: 'POS销售提醒', value: 'pos_info', type: 'input', clear: true, width: 955 },
  { label: '商品说明', value: 'item_desc', type: 'TextArea', clear: true, width: 955 },
];

export const moreCodeArr: ArtColumn[] = [
  // {
  //   name: '操作',
  //   code: 'operation',
  //   align: 'center',
  //   width: 60
  // },
  { name: '序号', code: '_index', align: 'center', width: 60 },
  { name: '商品条码', code: 'bar_code', width: 140 },
  { name: '仅用于采购出入库', code: 'only_purchase', width: 150 },
  { name: '外箱码', code: 'package_bar_code', width: 150 },
  { name: '零售条码', code: 'sale_bar_code', width: 150 },
  { name: '' },
];

const groupDetailArr: ArtColumn[] = [
  { name: '序号', code: '_index', align: 'center', width: 60 },
  { name: '商品代码', code: 'code', width: 100 },
  { name: '商品条码', code: 'bar_code', width: 100 },
  { name: '商品名称', code: 'name', width: 280 },
  { name: '商品类型', code: 'item_type', width: 100 },
  { name: '基本单位', code: 'unit', width: 100 },
  { name: '数量', code: 'quantity', width: 100 },
  { name: '' },
];
const levelArr: ArtColumn[] = [
  { name: '序号', code: 'index', width: COLUMN_WIDTH_ESUM.INDEX, align: 'center', lock: true },
  { name: '商品代码', code: 'code', width: COLUMN_WIDTH_ESUM.ITEM_CODE, features: { sortable: true } },
  { name: '商品条码', code: 'bar_code', width: COLUMN_WIDTH_ESUM.ITEM_BAR_CODE, features: { sortable: true } },
  { name: '速记码', code: 'shorthand_code', width: COLUMN_WIDTH_ESUM.SHORTHAND_CODE, features: { sortable: true } },
  { name: '分级名称', code: 'name', width: 280, features: { sortable: true } },
  { name: '分级类型', code: 'type', width: 100, features: { sortable: true } },
  { name: '基本单位', code: 'unit', width: 100, features: { sortable: true } },
  { name: '前台议价', code: 'pos_bargain', width: 100, features: { sortable: true } },
  { name: '前台折扣', code: 'pos_discount', width: 100, features: { sortable: true } },
  { name: '是否积分', code: 'point', width: 100, features: { sortable: true } },
  { name: '标准售价', code: 'sale_price', width: 110, features: { sortable: true } },
  { name: '会员价', code: 'sale_price_s', width: 110, features: { sortable: true } },
  { name: '售价3', code: 'sale_price_t', width: 110, features: { sortable: true } },
  { name: '售价4', code: 'sale_price_f', width: 110, features: { sortable: true } },
  { name: '修改人', code: 'update_by', width: 90, features: { sortable: true } },
  { name: '修改时间', code: 'update_time', width: 160, features: { sortable: true } },
];
const relatedArr: ArtColumn[] = [
  { name: '序号', code: '_index', align: 'center', width: 60 },
  { name: '商品代码', code: 'code', width: 100 },
  { name: '商品条码', code: 'bar_code', width: 100 },
  { name: '商品名称', code: 'name', width: 280 },
  { name: '商品类型', code: 'item_type', width: 100 },
  { name: '' },
];
const suppilerArr: ArtColumn[] = [
  { name: '序号', code: 'index', align: 'center', width: 60 },
  { name: '供应商', code: 'supplier_name', width: 100 },
  { name: '基本单位', code: 'basic_unit', width: 100 },
  { name: '基本单价', code: 'basic_price', width: 100 },
  { name: '采购单位', code: 'purchase_unit', width: 100 },
  { name: '采购单价', code: 'purchase_price', width: 100 },
  { name: '主供应商', code: 'main', width: 100 },
  { name: '最近收货日期', code: 'latest_time', width: 180 },
  { name: '' },
];
const qualificationArr: ArtColumn[] = [
  { name: '序号', code: 'index', align: 'center', width: 60 },
  { name: '生产商', code: 'name', width: 200 },
  { name: '产地', code: 'origin_place', width: 200 },
  { name: '默认', code: 'flag', width: 80 },
  { name: '' },
];
export default {
  titleArr,
  goodsInfoArr,
  unitCountArr,
  volumeWeightArr,
  priceSetArr,
  otherArr,
  moreCodeArr,
  groupDetailArr,
  levelArr,
  relatedArr,
  suppilerArr,
  qualificationArr,
};
