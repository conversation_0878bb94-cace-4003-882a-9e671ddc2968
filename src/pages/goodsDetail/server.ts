import { XlbFetch } from "@xlb/utils"

export const BASE_URL = process.env.BASE_URL
export default {
  // 查询
  read: (data: any) => XlbFetch.post(`${BASE_URL}/fsms/hxl.fsms.governmentrandomcheck.read`, data),
  // 删除
  delete: (data: any) => XlbFetch.post(`${BASE_URL}/fsms/hxl.fsms.governmentrandomcheck.delete`, data),
  // 导出
  export: (data: any) => XlbFetch.post(`${BASE_URL}/fsms/hxl.fsms.governmentrandomcheck.export`, data),
  // 附件上传
  upload: (data: any) => XlbFetch.post(`${BASE_URL}/fsms/hxl.fsms.governmentrandomcheck.file.upload`, data),
  // 食安处理
  foodsafetyhandling: (data: any) => XlbFetch.post(`${BASE_URL}/fsms/hxl.fsms.governmentrandomcheck.foodsafetyhandling`, data),
  // 营运处理
  operationshandling: (data: any) => XlbFetch.post(`${BASE_URL}/fsms/hxl.fsms.governmentrandomcheck.audit`, data),
  // 分页查询
  page: (data: any) => XlbFetch.post(`${BASE_URL}/fsms/hxl.fsms.governmentrandomcheck.page`, data),
  // 子公司品控处理
  qualitycontrolhandling: (data: any) => XlbFetch.post(`${BASE_URL}/fsms/hxl.fsms.governmentrandomcheck.handle`, data),
  // 操作记录查询
  record: (data: any) => XlbFetch.post(`${BASE_URL}/fsms/hxl.fsms.governmentrandomcheck.record.find`, data),
  // 新增
  save: (data: any) => XlbFetch.post(`${BASE_URL}/fsms/hxl.fsms.governmentrandomcheck.save`, data),
  // 上传结果
  updateResult: (data: any) => XlbFetch.post(`${BASE_URL}/fsms/hxl.fsms.governmentrandomcheck.update`, data),
  // 已结账单查询
  readbyserialnumber: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber`, data),
  // 查询生产商列表
  producerFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find`, data),
  // 供应商查询接口
  supplierFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find`, data),
  // 通知供应商
  notice: (data: any) => XlbFetch.post(`${BASE_URL}/fsms/hxl.fsms.governmentrandomcheckstoredetail.notice`, data),
  // 复检结果上传
  recheckUpload: (data: any) => XlbFetch.post(`${BASE_URL}/fsms/hxl.fsms.governmentrandomcheckstoredetail.recheck.upload`, data),
  // 复检
  recheck: (data: any) => XlbFetch.post(`${BASE_URL}/fsms/hxl.fsms.governmentrandomcheckstoredetail.recheck`, data),
}
