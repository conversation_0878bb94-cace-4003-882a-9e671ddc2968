import {FC, useEffect, useState} from 'react';
import {
  ContextState,
  XlbPageContainer,
  XlbButton,
  XlbBasicForm,
  XlbTableColumnProps,
  XlbIcon as IconFont, XlbTable
} from '@xlb/components';
import { COLUMN_WIDTH_ESUM } from '@/constants/common'
import { typeObj} from './data'
import { useKeepAliveRefresh } from '@/hooks'
import {useLocation} from 'react-router-dom'
import {read, sortSave} from '@/pages/approvalData/service.ts';
import {Form, Input, message} from 'antd';
import styles from './item.module.less'

const { ToolBtn} = XlbPageContainer

const Index: FC = () => {
  const location = useLocation()
  const { back } = useKeepAliveRefresh()
  const record = location.state as any
  const [data, setData] = useState<any>([])
  const [loading, setLoading] = useState<boolean>(false)

  const [form] = XlbBasicForm.useForm()

  const prevPost = () => {
    console.log(form.getFieldsValue(true));
    return { ...form.getFieldsValue(true) }
  }

  const Columns: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: COLUMN_WIDTH_ESUM.INDEX + 20,
      align: 'center',
    },
    {
      name: '字段名称',
      code: 'name',
      features: { sortable: true },
      width: 136,
    },
    {
      name: 'API Name',
      code: 'business_field',
      features: { sortable: true },
      width: 200,
    },
    {
      name: '字段类型',
      code: 'type',
      features: { sortable: true },
      width: 102,
      render: (text: string) => (
        <span>
          {typeObj[text]}
        </span>
      ),
    },
    {
      name: '创建时间',
      code: 'create_time',
      features: { sortable: true },
      width: COLUMN_WIDTH_ESUM.TIME,
    },
    {
      name: '',
    }
  ]
  const readFun = async () => {
    if(!record?.id) return
    setLoading(true)
    const res = await read({...form.getFieldsValue(true), approval_data_id: record?.id })
    setLoading(false)
    if(res?.code === 0){
      setData(res?.data || [])
    }
  }

  const sortSaveFun = async (data)=> {
    const res = await sortSave({...form.getFieldsValue(true), info: data})
    if(res?.code === 0){
      message.success('操作成功')
      readFun()
    }
  }


  useEffect(() => {
    if(!record?.id) return
    form.setFieldValue('approval_data_id', record?.id)
    readFun()
  }, [])
  let refresh: Function
  return (
    <XlbPageContainer
      immediatePost
      url=""
      tableColumn={Columns}
      prevPost={prevPost}
    >
      <div style={{padding: 16, display: 'flex', alignItems: 'center'}}>
        <div style={{width: 33, height: 33, borderRadius: 6, backgroundColor: '#3D66FE', padding: 6, marginRight: 12}}>
          <IconFont name="moban" color="#fff" size={21}/>
        </div>
        <p style={{color: '#1D2129', fontSize: 24, fontWeight: 500}}>{record?.name}</p>
      </div>

      <Form form={form} layout="inline">
        <Form.Item label={'字段名称'} name={'name'} style={{marginBottom: 12, marginLeft: 16}}>
          <Input style={{width: 136}}/>
        </Form.Item>
      </Form>

      <ToolBtn showColumnsSetting={false}>
        {({fetchData}: ContextState<any>) => {
          refresh = fetchData
          return (
            <XlbButton.Group>
              <XlbButton
                type="primary"
                onClick={readFun}
                icon={<IconFont name="sousuo" color="currentColor" size={16}/>}
              >
                查询
              </XlbButton>
              <XlbButton
                type="primary"
                onClick={() => back('/paas/approvalData/index')}
                icon={<IconFont name="fanhui" color="currentColor" size={16}/>}
              >
                返回
              </XlbButton>
            </XlbButton.Group>
          )
        }}
      </ToolBtn>
      <div className={styles.tableBox}>
        <XlbTable
          style={{height: '100%'}}
          selectMode="single"
          primaryKey={'id'}
          keepDataSource={false}
          // dragSort={true}
          // onDragSortEnd={sortSaveFun}
          columns={Columns}
          isLoading={loading}
          dataSource={data}
          total={data.length}
        />
      </div>

    </XlbPageContainer>
)
}

export default Index
