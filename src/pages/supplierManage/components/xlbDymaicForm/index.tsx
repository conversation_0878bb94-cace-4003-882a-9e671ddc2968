import React, { FC, Fragment, PropsWithChildren, ReactNode, useMemo } from 'react';
import { Form, FormProps, FormInstance, Tooltip, Row, message, Input } from 'antd';
import XlbForm from './form';
import { ComponentType, DymaicTemplate, DymaicTemplateContentDetail } from './type';
import { XlbBlueBar } from '@xlb/components';
import './index.less';
import ItemContent from './ItemContent';
import classnames from 'classnames';

type MultiFormValues = {
  /**
   * 表单key - value;
   */
  [key: string]: any;
}

type FormValues = {
  /**
   * form id
   */
  id: number;
  /**
   * 模版名称
   */
  name: string;
  /**
   *
   */
  details: MultiFormValues;
}

type FormProviderProps = Parameters<typeof Form.Provider>['0'];


export interface DymaicFormProps extends Pick<FormProps, 'form' | 'onFinish' | 'disabled'> {
  /**
   * 变动项模版
   */
  template: DymaicTemplate[];
  /**
   * 变动项的值, 为 MultiFormValues的 json;
   */
  data?: MultiFormValues | string | Array<FormValues>;
  /**
   * 上传组件的url
   */
  action?: string;
}

const DymaicForm: FC<PropsWithChildren<DymaicFormProps>> = ({
  data,
  form,
  action,
  template,
  onFinish,
  disabled,
  children,
}) => {

  const json = useMemo(() => {
    let d: { [id: string]: MultiFormValues } = {};
    if (!data) {
      return d;
    }
    if (typeof data == 'string') {
      try {
        d = JSON.parse(data);
      } catch (e) {
        console.error('json解析变动项数据失败, 请检查')
      }
      return d;
    }

    if (Array.isArray(data)) {
      const dataArr: FormValues[] = data;
      d = dataArr.reduce((prev, next) => {
        const id = next.id;
        prev[id] = next.details;
        return prev;
      }, {} as { [id: string]: MultiFormValues })
      return d;
    }

    if (typeof data == 'object') {
      d = data;
      return d;
    }
    return d;
  }, [data])


  const formValidator = async (id: string, form) => {
    const target = template.find(tem => tem.id?.toString() == id);
    const data = await form.validateFields().catch(err => {
      message.error(`${target?.name}存在未填的必填项`)
      return Promise.reject(err);
    })
    return { name: target?.name, id: id, data: data };
  }


  const onFormFinish: FormProviderProps['onFormFinish'] = async (_, info) => {
    const forms = info.forms;
    const formEntries = Object.entries(forms);



    try {
      const data = await Promise.all(formEntries.map(([id, form]) => formValidator(id, form)))
      let mapper: FormValues[] = []
      if (data.length) {
        mapper = data.map(item => {
          const result: FormValues = {
            id: Number(item?.id),
            name: item?.name as string,
            details: item.data,
          }
          return result;
        });
      }
      onFinish?.(mapper);

    } catch (err) {
      console.error('表单错误', err)
    }
  }

  /**
   * 自定义验证
   * @returns
   */
  const customerValidator = (value: any, info: DymaicTemplateContentDetail): Promise<void> => {
    const { required, title, min, max, component_type } = info;
    if (component_type != 'NUMBER') {
      if (required && !value) {
        return Promise.reject(new Error(`${title}必填`));
      }

      if (max && value?.length > max) {
        return Promise.reject(new Error(`${title}超过最大限制${max}`));
      }

      if (min && value?.length < min) {
        return Promise.reject(new Error(`${title}超过最小限制${min}`));
      }
    }

    if (component_type == 'NUMBER') {
      if (required && (value === undefined || value === null)) {
        return Promise.reject(new Error(`${title}必填`));
      }
      if (max && Number(value) > max) {
        return Promise.reject(new Error(`${title}超过最大${max}`));
      }
      if (min && Number(value) < min) {
        return Promise.reject(new Error(`${title}超过最小${min}`));
      }
    }

    if (component_type == 'MULTI_CHOICE') {
      if (required && (value === undefined || value === null || value.length == 0)) {
        return Promise.reject(new Error(`${title}必填`));
      }
    }

    return Promise.resolve();

  }

  /**
   * 自定义label渲染
   */
  const renderLabel = (info: DymaicTemplateContentDetail): ReactNode => {
    const { title, memo } = info;
    if (!memo) {
      return title;
    }

    return (
      <Row align='middle'>
        <span>{title}</span>
        <Tooltip title={memo}>
          <span className='iconfont icon-bangzhu' style={{ marginLeft: 2 }}></span>
        </Tooltip>
      </Row>
    )

  }

  // 过滤掉未启用的表单
  template = template.filter(item => item.enable);

  // 获取children
  const childrenArray = React.Children.toArray(children);

  // 过滤掉非ReactNode
  const childrenArrayByReactNode = childrenArray.filter(vnode => {
    return React.isValidElement(vnode);
  }) as React.ReactPortal[];

  // 过滤掉没有添加key值的node
  const childrenArrayByReactNodeAndKey = childrenArrayByReactNode.filter(vnode => {
    return !!vnode.key
  }) as Required<React.ReactPortal>[];

  const childrenNode = childrenArray;

  const replaceNode: { [key: string]: React.ReactPortal } = {};
  // 用模版去匹配带key的 vnode;
  if (childrenArrayByReactNodeAndKey.length) {
    childrenArrayByReactNodeAndKey.forEach(vnode => {
      const key = vnode.key as string;
      const target = template.find(tem => {
        const name = tem.name as string;
        return key.indexOf(name) > -1;
      });
      // 如果匹配上了，将其从children中剔除;
      if (target) {
        const name = target.name as string;
        replaceNode[name] = vnode;
        const index = childrenNode.indexOf(vnode);
        if (index > -1) {
          childrenNode.splice(index, 1);
        }
      }
    })
  }

  return (
    <div className={classnames('dymaic-form', { 'dymaic-form-disabled': disabled })}>
      <Form.Provider onFormFinish={onFormFinish} >
        {childrenNode}
        {
          template.map((item, index) => {
            let details = item.content?.details;
            if (item.name === undefined || item.name === null) {
              console.error(`${item.name}模版存在问题。name字段为空`)
              return;
            }

            if (!Array.isArray(details)) {
              console.error(`${item.name}模版存在问题。details字段非数组`)
              return;
            }

            if (replaceNode[item.name]) {
              if (!item.edit_show && !disabled) {
                return null
              }
              return replaceNode[item.name];
            }

            const formContent = json[item.id as number];

            return (
              <Fragment key={index}>
                <XlbBlueBar title={item.name} activeKey={item.name} containerStyle={{ marginBottom: 12 }} />
                <div className={'dymaic-change-form'} style={{ marginBottom: 12 }}>
                  <XlbForm name={item.id?.toString()} key={item.id} initialValues={formContent}>
                    {
                      details.map((item, index) => {
                        const { title, component_type, required, max, min, memo, id, show } = item;

                        const componentType = component_type as ComponentType;
                        if (title == undefined || title === null) {
                          console.error('字段名称不能为空')
                          return <div></div>;
                        }
                        return (
                          <>
                            {show ? <XlbForm.Item
                              key={index}

                              label={renderLabel(item)}
                              name={id}
                              disabled={disabled}
                              type={componentType}
                              required={required}
                              getValueFromEvent={(e) => {
                                if (componentType == 'MULTI_CHOICE') {
                                  return e?.map((item: string) => ({ label: item, value: item }));
                                }

                                if (componentType == 'TEXT' || componentType == "LONG_TEXT") {
                                  return e.target.value;
                                }

                                return e;
                              }}
                              rules={[() => ({
                                validator(_, value) {
                                  return customerValidator(value, item);
                                },
                              }),]}
                            >
                              <ItemContent data={item} action={action} />
                            </XlbForm.Item> : null}
                          </>
                        )
                      })
                    }
                  </XlbForm>
                </div>
              </Fragment>
            )
          })
        }
        <Form form={form} hidden></Form>
      </Form.Provider>
    </div>
  )
}

export default DymaicForm;
