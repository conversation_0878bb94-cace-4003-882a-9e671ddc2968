import { FC, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { XlbBasicForm, XlbBlueBar, XlbButton, XlbUploadFile, XlbIcon as IconFont } from '@xlb/components';
import { Tabs, message } from 'antd';
import Api from './server';
import styles from './index.module.less';
import isEmpty from 'lodash/isEmpty';
import { ContainerWithPadding } from '@/components';
import BaseInfo from './components/BaseInfo';
import { FilesType } from './type/type';
import { API_BASE_URL } from '@/constants/common';
import DocumentFiles from './components/documentFiles';
import AssociationSupplier from './components/associationSupplier';
import ParentChildSupplier from './components/parentChildSupplier';
import ProducerSuppliers from './components/ProducerSuppliers';
import useStore from './store';
import { SupplierResDTO } from './type/response';
import { hasAuth } from '@/utils';
import { useKeepAliveRefresh } from '@/hooks';
import XlbDymaicForm from './components/xlbDymaicForm';
import { formatArea } from '../supplierAccess/utils';
import { useFactoryCooperationForm } from '@/hooks/supplier';

const Item: FC = () => {
  const { back } = useKeepAliveRefresh();
  const record = useLocation().state as any;
  const location = useLocation();
  const supplierStore = useStore();
  const [form] = XlbBasicForm.useForm();
  const [customForm] = XlbBasicForm.useForm();
  const [tabItemLabel, setTabItemLabel] = useState<string>('TRADER');
  const [activeKey, setActiveKey] = useState<string>('1');
  const [loading, setLoading] = useState<boolean>(false);
  const [supplierBusinessEnvironment, setSupplierBusinessEnvironment] = useState<any>([]);
  const [customzeData, setCustomData] = useState([]);
  const { transformRegionInfo } = useFactoryCooperationForm(form, () => false);

  const [patypev, setPaytypev] = useState(0); //付款日期
  const getCustomizeData = async () => {
    let res = await Api.getCustomApi({ type: 'SUPPLIER' });
    setCustomData(res.data);
  };
  const readInfo = async (id: number) => {
    setLoading(true);
    const res = await Api.readItem({ id });
    setLoading(false);
    if (res.code === 0) {
      initForm(res?.data);
      setActiveKey('1');
      setTabItemLabel(res.data?.supplier_type || 'TRADER');
      // copy的情况下生成供应商代码
      if (record?.type === 'copy') {
        const code_res = await Api.createSupplierCode({ id: id });
        if (code_res?.code === 0) {
          form.setFieldsValue({
            ...form.getFieldsValue(true),
            code: code_res?.data?.code,
            name: code_res?.data?.name,
            id: null,
          });
        }
      }
    }
  };
  const initForm = (data: SupplierResDTO | undefined) => {
    if (!data) return;
    form.setFieldsValue({
      ...data,
      id: record?.type === 'copy' ? null : data?.id,
      region_info: formatArea(data || {}),
      category_name: data?.supplier_category?.name,
      kingdee_supplier_category: data?.supplier_category?.kingdee_supplier_category,
    });

    setPaytypev(data?.pay_type || 0);
    supplierStore.setDetails({ ...data, id: record?.type === 'copy' ? null : data?.id });
    setSupplierBusinessEnvironment(data?.supplier_business_environment || []);
  };
  const init = () => {
    if (record?.type === 'supplierAccess') {
      initForm(record.data);
      setTabItemLabel(record.data?.supplier_type || 'TRADER');
      return;
    }

    if (!!record?.id) {
      readInfo(record?.id);
      initForm(record);
    } else {
      form.setFieldsValue({
        shared: 0,
        settlement_store: 2,
        settlement_mode: 0,
        pay_type: 0,
        purchase_period: 0,
        unloading_fee: '0.00',
        supplier_type: 'TRADER',
        actived: true,
      });

      if (!isEmpty(record?.selectTreeNode)) {
        form.setFieldsValue({
          category_name: record.selectTreeNode.name,
          category_id: record.selectTreeNode.id,
        });
      }
      autoGetCategoryCode(record?.categoryCode);
    }
  };

  const handleBack = (isrefersh: boolean = true) => {
    back('/file/supplierManage', isrefersh);
  };
  const autoGetCategoryCode = async (code: string) => {
    if (!code) return;
    const res = await Api.getCode({ code });
    if (res.code === 0) {
      res.data && form.setFieldValue('code', res.data);
    }
  };

  const items = [
    {
      key: '1',
      label: '供应商信息',
      children: (
        <div style={{ overflowY: 'auto', maxHeight: 'calc(100vh - 205px)' }}>
          <BaseInfo
            form={form}
            setTabItemLabel={setTabItemLabel}
            autoGetCategoryCode={autoGetCategoryCode}
            patypev={patypev}
            setPaytypev={setPaytypev}
          />
          <DocumentFiles />
          <XlbBlueBar title="经营生产环境" hasMargin />
          <XlbUploadFile
            accept={'image'}
            key={'经营生产环境'}
            action={`${API_BASE_URL}/center/hxl.center.file.upload`}
            showIndex={false}
            fileList={supplierBusinessEnvironment || []}
            deleteByServer={false}
            listType="picture"
            data={{
              refType: FilesType.SUPPLIER_BUSINESS_ENVIRONMENT,
              refId: supplierStore.details?.id,
            }}
            onChange={(e) => {
              const item = e?.map((item) => {
                return {
                  id: item?.id,
                  name: item?.name,
                  url: item?.url,
                  suffix_type: item?.suffix_type,
                };
              });
              setSupplierBusinessEnvironment(item);
            }}
          />
          <XlbDymaicForm
            onFinish={(v) => ClickSave(v)}
            form={customForm}
            template={customzeData}
            data={supplierStore.details?.custom_attribute}
            disabled={false}
            action={`${API_BASE_URL}/center/hxl.center.file.upload`}
            // action={`${API_BASE_URL}/center/hxl.center.file.convertHome.upload`}
          />
        </div>
      ),
    },
    {
      key: '2',
      label: tabItemLabel === 'TRADER' ? '关联生产商' : '关联贸易商',
      children: (
        <>
          <AssociationSupplier getRead={readInfo} supplier_type={tabItemLabel} />
        </>
      ),
    },
    {
      key: '3',
      label: supplierStore.details?.supplier_identity == 'CHILD' ? '关联父供应商' : '关联子供应商',
      children: (
        <>
          <ParentChildSupplier />
        </>
      ),
    },
    {
      key: '4',
      label: '关联生产商',
      children: (
        <>
          <ProducerSuppliers getRead={readInfo} />
        </>
      ),
    },
  ];

  const isFileCenterIdNull = (list: any[]) => {
    if (Array.isArray(list)) {
      const arr = list?.filter((v) => !v.center_id);
      if (arr?.length) {
        return true;
      }
      return false;
    }
    return false;
  };

  const ClickSave = async (value: any) => {
    if (!(await form.validateFields())) return;
    if (Array.isArray(value)) {
      value.forEach((form) => {
        const details = form.details;
        Object.values(details).forEach((detailsValues) => {
          if (Array.isArray(detailsValues)) {
            detailsValues.forEach((item) => {
              if (item.originFileObj) {
                delete item.originFileObj;
              }
            });
          }
        });
      });
    }
    let customzeData = await Api.getCustomApi({ type: 'SUPPLIER' });
    // console.log('res', customzeData.data);
    let enableLength = customzeData.data.filter((v) => v.enable);

    if (!value && enableLength?.length) return;
    const newSupplierQualityCertificateIds = supplierStore.supplierQualityCertificate?.map((v) => v.id) || [];
    const old_supplier_business_licences =
      supplierStore?.details?.supplier_business_licences?.filter((v) => !v.archive_date) || [];
    const old_supplier_business_operation =
      supplierStore?.details?.supplier_business_operation?.filter((v) => !v.archive_date) || [];
    const old_supplier_production = supplierStore?.details?.supplier_production?.filter((v) => !v.archive_date) || [];
    const old_supplier_food_business =
      supplierStore?.details?.supplier_food_business?.filter((v) => !v.archive_date) || [];
    const old_supplier_food_production =
      supplierStore?.details?.supplier_food_production?.filter((v) => !v.archive_date) || [];
    const old_supplier_quality_certificate =
      supplierStore?.details?.supplier_quality_certificate
        ?.filter((v) => !v.archive_date)
        ?.map((v) => {
          if (!newSupplierQualityCertificateIds.includes(v.id)) {
            return { ...v, archive_flag: true };
          }
          return v;
        }) || [];

    const association = [...supplierStore?.suppliers, ...supplierStore?.supplierAssociationProducerSuppliers];

    const payload = {
      ...form.getFieldsValue(true),
      actived: !!form.getFieldValue('actived') ? 1 : 0,
      supplier_business_environment: supplierBusinessEnvironment,
      supplier_food_production_details: supplierStore.supplier_food_production_details,

      supplier_business_licences: isFileCenterIdNull(supplierStore.supplierBusinessLicences)
        ? [...supplierStore.supplierBusinessLicences, ...old_supplier_business_licences]
        : supplierStore.supplierBusinessLicences,

      supplier_business_operation: isFileCenterIdNull(supplierStore.supplierBusinessOperation)
        ? [...supplierStore.supplierBusinessOperation, ...old_supplier_business_operation]
        : supplierStore.supplierBusinessOperation,

      supplier_production: isFileCenterIdNull(supplierStore.supplierProduction)
        ? [...supplierStore.supplierProduction, ...old_supplier_production]
        : supplierStore.supplierProduction,

      supplier_food_business: isFileCenterIdNull(supplierStore.supplierFoodBusiness)
        ? [...supplierStore.supplierFoodBusiness, ...old_supplier_food_business]
        : supplierStore.supplierFoodBusiness,

      supplier_food_production: isFileCenterIdNull(supplierStore.supplierFoodProduction)
        ? [...supplierStore.supplierFoodProduction, ...old_supplier_food_production]
        : supplierStore.supplierFoodProduction,
      supplier_quality_certificate: isFileCenterIdNull(supplierStore.supplierQualityCertificate)
        ? [...supplierStore.supplierQualityCertificate, ...old_supplier_quality_certificate]
        : supplierStore.supplierQualityCertificate,

      association_ids: association?.map((item) => item.detail_supplier_id),
      custom_attribute: value,
    };
    const data = transformRegionInfo(payload);
    setLoading(true);
    const res =
      !!location.state?.id && record?.type !== 'copy'
        ? await Api.updateItem({ ...data, id: supplierStore.details.id })
        : await Api.addItem(data);
    setLoading(false);
    if (res.code === 0) {
      message.success('操作成功');
      handleBack();
    }
  };

  useEffect(() => {
    init();
    getCustomizeData();
    return () => {
      supplierStore.restStore();
    };
  }, []);

  const formSubmit = () => {
    form.submit();
    customForm.submit();
  };
  const getTab = () => {
    const tabList = items
      .filter((v) => {
        return !supplierStore.details?.id || supplierStore.details?.supplier_identity == 'NORMAL' ? v.key !== '3' : v;
      })
      .filter((v) => (tabItemLabel == 'TRADER' ? v.key !== '4' : v));
    return tabList;
  };
  return (
    <ContainerWithPadding>
      <XlbButton.Group>
        {hasAuth(['供应商管理', '编辑']) && (
          <XlbButton
            type="primary"
            onClick={() => formSubmit()}
            loading={loading}
            icon={<IconFont name="baocun" color="currentColor" size={16} />}
          >
            保存
          </XlbButton>
        )}
        <XlbButton
          type="primary"
          onClick={() => handleBack(false)}
          icon={<IconFont name="fanhui" color="currentColor" size={16} />}
        >
          返回
        </XlbButton>
      </XlbButton.Group>
      <div>
        <Tabs activeKey={activeKey} items={getTab()} className={styles.tabs} onChange={(e) => setActiveKey(e)} />
      </div>
    </ContainerWithPadding>
  );
};

export default Item;
