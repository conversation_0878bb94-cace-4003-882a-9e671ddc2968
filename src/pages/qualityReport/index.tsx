import {
  XlbButton,
  XlbIcon,
  XlbProPageContainer,
  XlbTableColumnProps,
  XlbModalForm,
  XlbDrawer,
  XlbTimeLine,
} from '@xlb/components';
import { channelEmail, stateEmail, qualityTypeEmail, filesEmail, handleTypeEmail, operation } from './data';
import React, { useEffect, useState } from 'react';
import { CenterFieldKeyMap } from '@/constants/config/center';
import { hasAuth } from '@/utils';
import Avatar from '@/assets/images/xlb_erp/defaultAvatar.png';
import { XlbFetch } from '@xlb/utils';
import { API_BASE_URL } from '@/constants/common';

const Index: React.FC = () => {
  const tableList: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 70,
      align: 'center',
    },
    {
      name: '质检单号',
      code: 'fid',
      width: 190,
      features: { sortable: true, details: true },
    },
    {
      name: '所属公司',
      code: 'company_id',
      width: 130,
      features: { sortable: true },
      render(text, record, index) {
        return record.company_name;
      },
    },
    {
      name: '门店',
      code: 'store_id',
      width: 144,
      features: { sortable: true },
      render(text, record, index) {
        return record.store_name;
      },
    },
    {
      name: '供应商',
      code: 'supplier_id',
      width: 116,
      features: { sortable: true },
      render(text, record, index) {
        return record.supplier_name;
      },
    },
    {
      name: '生产商',
      code: 'producer_id',
      width: 116,
      features: { sortable: true },
      render(text, record, index) {
        return record.producer_name;
      },
    },
    {
      name: '产地',
      code: 'origin_place',
      width: 116,
      features: { sortable: true },
    },
    {
      name: '商品代码',
      code: 'item_code',
      width: 100,
      features: { sortable: false },
    },
    {
      name: '商品名称',
      code: 'item_id',
      width: 256,
      features: { sortable: true },
      render(text, record, index) {
        return record.item_name;
      },
    },
    {
      name: '零售规格',
      code: 'item_spec',
      width: 116,
      features: { sortable: false },
    },
    {
      name: '生产日期',
      code: 'productor_date',
      width: 102,
      features: { sortable: true },
    },
    {
      name: '生产批次',
      code: 'batch_number',
      width: 102,
      features: { sortable: true },
    },
    {
      name: '商品数量',
      code: 'item_quantity',
      width: 88,
      features: { sortable: true },
    },
    {
      name: '商品分类',
      code: 'item_category_name',
      width: 116,
      features: { sortable: true },
    },
    {
      name: '商品单位',
      code: 'item_unit',
      width: 80,
      features: { sortable: true },
    },
    {
      name: '食品分类',
      code: 'food_category_name',
      width: 116,
      features: { sortable: true },
    },
    {
      name: '商品品牌',
      code: 'item_brand_name',
      width: 116,
      features: { sortable: false },
    },
    {
      name: '上游配送中心',
      code: 'upstream_center_name',
      width: 116,
      features: { sortable: false },
    },
    {
      name: '渠道',
      code: 'channel',
      width: 88,
      features: { sortable: true },
      render(text: any) {
        return <div>{channelEmail.find((item) => item.value === text)?.label}</div>;
      },
    },
    {
      name: '质量类型',
      code: 'quality_type',
      width: 88,
      features: { sortable: true },
      render(text: any) {
        return <div>{qualityTypeEmail.find((item) => item.value === text)?.label}</div>;
      },
    },
    {
      name: '一级类目问题',
      code: 'problem_name',
      width: 116,
      features: { sortable: true },
    },
    {
      name: '二级类目问题',
      code: 'one_category_name',
      width: 116,
      features: { sortable: true },
    },
    {
      name: '三级类目问题',
      code: 'two_category_name',
      width: 116,
      features: { sortable: true },
    },
    {
      name: '状态',
      code: 'state',
      width: 88,
      features: { sortable: true },
      render(text: any) {
        const obj = stateEmail.find((item) => item.value === text);
        return <div className={obj?.type}>{obj?.label}</div>;
      },
    },
    {
      name: '处理人',
      code: 'handle_by',
      width: 116,
      features: { sortable: true },
    },
    {
      name: '提交时间',
      code: 'submit_time',
      width: 162,
      features: { sortable: true },
    },
    {
      name: '处理时间',
      code: 'handle_time',
      width: 162,
      features: { sortable: true },
    },
    {
      name: '处理周期',
      code: 'handle_cycle',
      width: 162,
      features: { sortable: true },
    },
    {
      name: '操作',
      code: 'option',
      width: 88,
      render: (_, record: any) => {
        return (
          <XlbButton
            type="text"
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              getData(record);
            }}
          >
            操作记录
          </XlbButton>
        );
      },
    },
  ];

  const auditList = [
    {
      id: 'commonInput',
      label: '审核意见',
      name: 'memo',
      rules: [{ required: true, message: '请输入审核意见' }],
    },
    {
      id: 'commonUpload',
      label: '附件',
      name: 'audit_files',
      fieldProps: {
        action: '/erp-mdm/hxl.erp.file.upload',
        mode: 'textButton',
        listType: 'text',
        hiddenControlerIcon: false,
        deleteByServer: false,
        showUpload: true,
        data: {
          refType: 'SUPPLIER_AUDIT',
          refId: 'sfsffefefe',
        },
      },
    },
  ];
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<any[]>([]);

  const getData = async (data: any) => {
    const res = await XlbFetch.post(`${API_BASE_URL}/center/hxl.center.qualityreportapprove.find`, data);
    setOpen(true);
    if (res.code === 0) {
      setData(res.data);
    }
  };

  return (
    <>
      <XlbDrawer placement={'right'} title="操作记录" onClose={() => setOpen(false)} open={open} footer={false}>
        <XlbTimeLine
          mode="left"
          items={data.map((item: any, index: number) => ({
            activeKey: index,
            color: 'gray',
            label: <div style={{ fontWeight: 500, fontSize: 16 }}>{item.operate}</div>,
            children: (
              <div style={{ display: 'flex', alignItems: 'center', gap: 10 }}>
                <img width={36} height={36} src={Avatar} />
                <div style={{ flex: 1 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span style={{ fontSize: 14, fontWeight: 400 }}>{item.create_by}</span>
                    <span style={{ fontSize: 12, color: '#86909C' }}>{item.create_time}</span>
                  </div>
                  <span className={operation.find((i) => i.value == item.operate)?.type} style={{ fontSize: 12 }}>
                    {operation.find((i) => i.value == item.operate)?.value}
                  </span>
                </div>
              </div>
            ),
          }))}
          defaultActiveKey={['1', '2']}
        />
      </XlbDrawer>
      <XlbProPageContainer
        details={{
          primaryKey: 'fid',
          readOnly: true,
          hiddenSaveBtn: true,
          queryFieldProps: {
            url: ['/center/hxl.center.qualityreport.read', '/center/hxl.center.qualityreport.check'],
            afterPost: (data) => {
              const val = { ...data[0], check: data[1] };
              if (val?.files?.length) {
                val.files.map((item: any) => {
                  const obj = filesEmail.find((i) => i.value == item.ref_type);
                  if (!obj) return undefined;
                  val[obj.label] = item?.files;
                });
              }
              if (val.suggestions?.length) {
                val.suggestions.map((item: any, index: number) => {
                  val[`suggestions_${index}`] = item?.approval_suggestion;
                });
              }
              return val;
            },
          },
          extra({ values, onClose }: any) {
            return (
              <>
                {hasAuth(['质量提报', '批复']) &&
                (values?.state === 'HANDLE_PASS' ||
                  (values?.state === 'APPROVAL_PROCESSED' && values?.check) ||
                  (values?.state === 'APPROVAL_PROCESSING' && !values?.check)) ? (
                  <XlbModalForm
                    onOk={() => onClose()}
                    formProps={{
                      itemSpan: 24,
                      initialValues: {},
                    }}
                    title="批复"
                    url={
                      values?.check
                        ? '/center/hxl.center.qualityreport.approve'
                        : '/center/hxl.center.qualityreport.submit'
                    }
                    isCancel={true}
                    formList={[
                      {
                        componentType: 'form',
                        fieldProps: {
                          formList: [
                            {
                              id: 'commoneTextArea',
                              name: 'approval_suggestion',
                              label: '批复建议',
                              fieldProps: { autoSize: { minRows: 2, maxRows: 4 } },
                              rules: [{ required: true, message: '批复建议不能为空' }],
                            },
                          ],
                        },
                      },
                    ]}
                    beforePost={(data: any) => {
                      if (values?.check) {
                        data.fid = values?.fid;
                      } else {
                        data.quality_report_fid = values?.fid;
                      }
                      return data;
                    }}
                  >
                    <XlbButton type="primary" icon={<XlbIcon name="tongguo" />}>
                      批复
                    </XlbButton>
                  </XlbModalForm>
                ) : null}
                {hasAuth(['质量提报', '转发']) && values?.state === 'HANDLE_PASS' && values?.check ? (
                  <XlbModalForm
                    onOk={() => onClose()}
                    formProps={{
                      itemSpan: 24,
                      initialValues: {},
                    }}
                    title="转发"
                    url="/center/hxl.center.qualityreport.forward"
                    isCancel={true}
                    formList={[
                      {
                        componentType: 'form',
                        fieldProps: {
                          formList: [
                            {
                              id: 'organizationId',
                              label: '组织',
                              fieldProps: {
                                multiple: true,
                              },
                              rules: [{ required: true, message: '组织不能为空' }],
                              onChange: (_, form, options) => {
                                form?.setFieldValue('organization_name', options);
                              },
                            },
                          ],
                        },
                      },
                    ]}
                    beforePost={(data: any) => {
                      return {
                        fid: values?.fid,
                        orgs: data.organization_id.map((v: any, i: number) => ({
                          organization_id: v,
                          organization_name: data.organization_name[i],
                        })),
                      };
                    }}
                  >
                    <XlbButton type="primary" icon={<XlbIcon name="fenxiang" />}>
                      转发
                    </XlbButton>
                  </XlbModalForm>
                ) : null}
              </>
            );
          },
          formList: [
            {
              componentType: 'blueBar',
              fieldProps: { title: '基本信息' },
              children: [
                {
                  componentType: 'form',
                  fieldProps: {
                    width: '100%',
                    formList: [
                      {
                        label: '质量类型',
                        name: 'quality_type',
                        id: 'commonSelect',
                        options: qualityTypeEmail,
                      },
                      {
                        label: '门店',
                        id: 'commonInput',
                        name: 'store_name',
                      },
                      {
                        label: '大区',
                        id: 'commonInput',
                        name: 'business_area_name',
                      },
                      {
                        label: '区域',
                        id: 'commonInput',
                        name: 'area',
                      },
                      {
                        label: '上游配送中心',
                        id: 'commonInput',
                        name: 'upstream_center_name',
                        disabled: true,
                      },
                      {
                        label: '提报日期',
                        id: 'createTime',
                        name: 'create_time',
                        disabled: true,
                      },
                      {
                        label: '渠道',
                        id: CenterFieldKeyMap.centerSelect,
                        name: 'channel',
                        options: channelEmail,
                        dependencies: ['quality_type'],
                        hidden: (formValues) => {
                          return formValues?.quality_type == 'SELF';
                        },
                      },
                      // {
                      //   label: '督导',
                      //   dependencies: ['quality_type'],
                      //   hidden: (formValues) => {
                      //     return formValues?.quality_type !== 'SELF';
                      //   },
                      //   id: 'commonInput',
                      //   name: 'name',
                      // },
                    ],
                  },
                },
              ],
            },
            {
              componentType: 'blueBar',
              fieldProps: { title: '商品信息' },
              children: [
                {
                  componentType: 'form',
                  fieldProps: {
                    width: '100%',
                    formList: [
                      {
                        label: '一级类目问题',
                        name: 'problem_name',
                        id: 'commonInput',
                        // options: [],
                      },
                      {
                        label: '二级类目问题',
                        name: 'one_category_name',
                        id: 'commonInput',
                        // options: [],
                      },
                      {
                        label: '三级类目问题',
                        name: 'two_category_name',
                        id: 'commonInput',
                        // options: [],
                      },
                      {
                        label: '商品名称',
                        name: 'item_name',
                        id: 'commonInput',
                        dependencies: ['item_name', 'item_expired'],
                        render(formValues, props) {
                          return (
                            <>
                              <div>{formValues?.item_name}</div>
                              {formValues?.item_expired && typeof formValues?.item_expired && (
                                <div style={{ color: '#faad14' }}>商品已过期</div>
                              )}
                            </>
                          );
                        },
                      },
                      {
                        label: '商品代码',
                        name: 'item_code',
                        id: 'commonInput',
                        disabled: true,
                      },
                      {
                        label: '商品品牌',
                        name: 'item_brand_name',
                        id: 'commonInput',
                        disabled: true,
                      },
                      {
                        label: '零售规格',
                        name: 'item_spec',
                        id: 'commonInput',
                        disabled: true,
                      },
                      {
                        label: '零售价',
                        name: 'sale_price',
                        id: 'commonInputNumber',
                        disabled: true,
                      },
                      {
                        label: '商品分类',
                        name: 'item_category_name',
                        id: 'commonInput',
                        disabled: true,
                      },
                      {
                        label: '商品单位',
                        name: 'item_unit',
                        id: 'commonInput',
                        disabled: true,
                      },
                      {
                        label: '食品分类',
                        name: 'food_category_name',
                        id: 'commonInput',
                        // options: [],
                      },
                      {
                        label: '生产商',
                        name: 'producer_name',
                        id: 'commonInput',
                        // options: [],
                      },
                      {
                        label: '供应商',
                        name: 'supplier_name',
                        id: 'commonInput',
                        // options: [],
                      },
                      {
                        label: '产地',
                        name: 'origin_place',
                        id: 'commonInput',
                        disabled: true,
                      },
                      {
                        label: '商品数量',
                        name: 'item_quantity',
                        id: 'commonInput',
                      },
                      {
                        label: '生产日期',
                        name: 'productor_date',
                        id: 'createTime',
                      },
                      {
                        label: '产品批次',
                        name: 'batch_number',
                        id: CenterFieldKeyMap.centerInput,
                        dependencies: ['quality_type'],
                        hidden: (formValues) => {
                          return formValues?.quality_type == 'CUSTOMER';
                        },
                      },
                      {
                        label: '问题商品照片',
                        name: 'problem',
                        id: CenterFieldKeyMap.centerUpload,
                        fieldProps: { buttonModal: true, mode: 'look', showUpload: false, listType: 'picture' },
                        dependencies: ['quality_type'],
                        hidden: (formValues) => {
                          return formValues?.quality_type == 'CUSTOMER';
                        },
                      },
                      {
                        label: '生产日期照片',
                        name: 'product',
                        id: CenterFieldKeyMap.centerUpload,
                        fieldProps: { buttonModal: true, mode: 'look', showUpload: false, listType: 'picture' },
                        dependencies: ['quality_type'],
                        hidden: (formValues) => {
                          return formValues?.quality_type == 'CUSTOMER';
                        },
                      },
                      {
                        label: '问题销毁照片',
                        name: 'destory',
                        id: CenterFieldKeyMap.centerUpload,
                        fieldProps: { buttonModal: true, mode: 'look', showUpload: false, listType: 'picture' },
                        dependencies: ['quality_type'],
                        hidden: (formValues) => {
                          return formValues?.quality_type == 'CUSTOMER';
                        },
                      },
                      {
                        label: '问题附件',
                        name: 'problem_item',
                        id: CenterFieldKeyMap.centerUpload,
                        fieldProps: { buttonModal: true, mode: 'look', showUpload: false, listType: 'picture' },
                        dependencies: ['quality_type'],
                        hidden: (formValues) => {
                          return formValues?.quality_type == 'SELF';
                        },
                      },
                      {
                        label: '问题描述',
                        name: 'detail_memo',
                        id: 'commoneTextArea',
                        fieldProps: { autoSize: { minRows: 2, maxRows: 4 } },
                      },
                    ],
                  },
                },
              ],
            },
            {
              componentType: 'blueBar',
              fieldProps: { title: '相关人信息' },
              children: [
                {
                  componentType: 'form',
                  fieldProps: {
                    width: '100%',
                    formList: [
                      {
                        label: '顾客姓名',
                        name: 'customer_name',
                        dependencies: ['quality_type'],
                        id: CenterFieldKeyMap.centerInput,
                        hidden: (formValues) => {
                          return formValues?.quality_type == 'SELF';
                        },
                      },
                      {
                        label: '发现人姓名',
                        name: 'find_by',
                        id: CenterFieldKeyMap.centerInput,
                        dependencies: ['quality_type'],
                        hidden: (formValues) => {
                          return formValues?.quality_type == 'CUSTOMER';
                        },
                      },
                      {
                        label: '联系电话',
                        name: 'tel',
                        id: 'commonInput',
                      },
                      {
                        label: '支付宝账户',
                        name: 'alipay_account',
                        id: CenterFieldKeyMap.centerInput,
                        dependencies: ['quality_type'],
                        hidden: (formValues) => {
                          return formValues?.quality_type == 'CUSTOMER';
                        },
                      },
                      {
                        label: '金额',
                        name: 'reward_money',
                        id: CenterFieldKeyMap.centerInput,
                        dependencies: ['quality_type'],
                        hidden: (formValues) => {
                          return formValues?.quality_type == 'CUSTOMER';
                        },
                      },
                      {
                        label: '诉求',
                        name: 'customer_demand',
                        id: CenterFieldKeyMap.centerTextArea,
                        fieldProps: { autoSize: { minRows: 2, maxRows: 4 } },
                        dependencies: ['quality_type'],
                        hidden: (formValues) => {
                          return formValues?.quality_type == 'SELF';
                        },
                      },
                    ],
                  },
                },
              ],
            },
            // {
            //   componentType: 'blueBar',
            //   fieldProps: { title: '审核结果' },
            //   dependencies: ['state'],
            //   children: (formValues) => [
            //     {
            //       componentType: 'form',
            //       fieldProps: {
            //         readOnly: true,
            //         width: '100%',
            //         formList: auditList,
            //       },
            //     },
            //   ],
            // },
            {
              componentType: 'blueBar',
              fieldProps: { title: '处理结果' },
              children: [
                {
                  componentType: 'form',
                  fieldProps: {
                    width: '100%',
                    formList: [
                      {
                        label: '处理类型',
                        name: 'handle_type',
                        id: 'commonSelect',
                        options: handleTypeEmail,
                      },
                      {
                        label: '赔付金额',
                        name: 'compensate_money',
                        id: 'commonInputNumber',
                      },
                      {
                        label: '附件',
                        name: 'handle_attachments',
                        id: 'commonUpload',
                        fieldProps: { buttonModal: true, mode: 'look', showUpload: false, listType: 'picture' },
                      },
                      {
                        label: '说明',
                        name: 'handle_memo',
                        id: 'commoneTextArea',
                      },
                    ],
                  },
                },
              ],
            },
            {
              componentType: 'blueBar',
              fieldProps: { title: '中心批复' },
              dependencies: ['state'],
              hidden(formValues: any) {
                return formValues.state != 'APPROVAL_PROCESSED' && formValues.state != 'END';
              },
              children(formValues) {
                return [
                  {
                    componentType: 'form',
                    fieldProps: {
                      width: '100%',
                      formList: formValues?.suggestions?.length
                        ? formValues?.suggestions.map((item: any, index: number) => ({
                            label: `${item.organization_name}批复建议`,
                            name: `suggestions_${index}`,
                            id: 'commoneTextArea',
                          }))
                        : [],
                    },
                  },
                ];
              },
            },
          ],
        }}
        searchFieldProps={{
          formList: [
            {
              id: 'dateCommon',
              name: 'date',
            },
            {
              label: '关键字',
              id: 'keyword',
            },
            {
              label: '所属公司',
              id: 'company',
              name: 'company_id',
            },
            {
              label: '门店',
              id: 'storeId',
              name: 'store_ids',
              fieldProps: {
                dialogParams: {
                  type: 'centerStore',
                  dataType: 'tree',
                  isMultiple: true,
                },
              },
            },
            {
              label: '商品档案',
              id: 'centerItemIds',
              name: 'item_ids',
            },
            {
              label: '供应商',
              name: 'supplier_ids',
              id: 'supplierIds',
            },
            {
              label: '生产商',
              id: CenterFieldKeyMap.centerQualityReportProducers,
              name: 'producer_id',
            },
            {
              label: '商品分类',
              name: 'item_category_ids',
              id: 'centerCategoryIds',
            },
            {
              label: '食品分类',
              name: 'food_category_id',
              id: CenterFieldKeyMap.centerQualityReportDelivery,
            },
            {
              label: '商品品牌',
              name: 'item_brand_id',
              id: CenterFieldKeyMap.centerItemBrand,
            },
            {
              label: '配送中心',
              name: 'upstream_center_id',
              id: CenterFieldKeyMap.centerQualityReportDeliveryCenter,
            },
            {
              label: '质量类型',
              name: 'quality_type',
              id: 'commonSelect',
              options: qualityTypeEmail,
            },
            {
              label: '一级类目问题',
              name: 'problem_id',
              id: CenterFieldKeyMap.centerQualityReportProblem,
            },
            {
              label: '单据号',
              name: 'fid',
              id: 'commonInput',
            },
            {
              label: '状态',
              name: 'state',
              id: 'commonSelect',
              options: stateEmail,
            },
            {
              label: '制单人',
              name: 'create_by',
              id: 'commonInput',
            },
          ],
        }}
        exportFieldProps={{
          url: hasAuth(['质量提报', '导出']) ? '/center/hxl.center.qualityreport.export' : undefined,
          fileName: '质量提报',
        }}
        tableFieldProps={{
          url: '/center/hxl.center.qualityreport.page',
          tableColumn: tableList,
          immediatePost: true,
          primaryKey: 'fid',
          // showColumnsSetting: false,
        }}
      />
    </>
  );
};

export default Index;
