import styles from './index.module.less';
import { Form, Layout, message } from 'antd';
import { useTablePipeline, features, ArtColumn, BaseTable } from 'ali-react-table';
import React, { useEffect, useRef, useState } from 'react';
import * as fusion from '@alifd/next';
import { authTypes } from '../data';
import Api from '../server';
import useColumns from './hooks/useColumns';
import { useKeepAliveRefresh } from '@/hooks';
import {
  getLeftTreeData,
  authMap,
  getTaleDataMap,
  diffList,
  actionMap,
  updateTableDataList,
  formateTableList2Tree,
  updateTableListItem,
  debounce,
  callerArr,
  generateTableDataTree,
  markAuthIds,
  judgeSelectedRow,
} from './util';
import type { TableList } from './util';
import type { CellProps, RoleInfo } from './type';
import { uniqBy } from 'lodash';
import { TableHeaderCheckBox, type TableHeaderCheckBoxRef } from './tableHeaderCheckBox';
import { hasAuth } from '@/utils';
import { XlbButton, XlbCheckbox, XlbIcon, XlbInput, XlbTipsModal, XlbTree, XlbTreeModal } from '@xlb/components';
import { useLocation } from 'react-router-dom';
import { keywordReqDTO } from '@/pages/goodsUnit/type/request';

const { Sider, Content } = Layout;

const headerMap = new Map<string, TableHeaderCheckBoxRef | null>();

// 表格样式简单配置
export const tableStyle = {
  '--header-row-height': '28px',
  '--row-height': '28px',
  '--header-bgcolor': '#fafafa',
  '--header-color': '#666',
  '--row-color': '#000',
  '--cell-padding': '0',
  fontSize: '13px',
};

const RoleManageItem = () => {
  const location = useLocation();

  /**
   * ? 新的状态 */
  const [roleInfos, setRoleInfos] = useState<RoleInfo[]>([]);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const [typeName, setTypeName] = useState('');

  const [tableData, setTableData] = useState<TableList[]>([]);

  const { back } = useKeepAliveRefresh();
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]); // 展开的节点
  const [filterName, setfilterName] = useState<any>([]);
  const [form] = Form.useForm();
  const [preId, setId] = useState();
  const [isLoading, setisLoading] = useState<boolean>(false);
  const [rowData, setRowData] = useState<any[]>([]);
  const { columns, columnsMap, setColumnsMap, computeCol } = useColumns([]);
  const [itemArr, setItemArr] = useState<ArtColumn[]>([]);
  // 左侧树
  const [treeData, setTreeData] = useState<any>([]);
  const [defaultSelectedKeys, setDefaultSelectedKeys] = useState<any>(['CENTER中心/档案/基础资料']);
  const [, setTreeLoading] = useState<boolean>(false);

  const tableDataRef = useRef<TableList[]>([]);

  const selectedIdsRef = useRef<number[]>(selectedIds);

  const updateTableData = (lists: TableList[]) => {
    setTableData(lists);
    tableDataRef.current = lists;

    setRowData(formateTableList2Tree(JSON.parse(JSON.stringify(lists))));
    // tableRender()
  };

  const handleSelectRow = (differNames: string[], checked: boolean) => {
    const targetLists = tableData.filter((item) => differNames.includes(item.main_key!));
    let ids = selectedIds.slice();

    const actionsArr = targetLists.map((item) => item.authority_details).flat(1);
    const actionIds = actionsArr.map((item) => item.id);
    if (checked) {
      ids = ids.concat(actionIds);
    } else {
      ids = ids.filter((item) => !actionIds.includes(item));
    }

    const tableLists = tableData.slice();
    const curTableLists = tableLists.map((item) => {
      if (differNames.includes(item.main_key!)) {
        return updateTableListItem(item, checked);
      }
      return item;
    });

    setSelectedIds(ids);
    selectedIdsRef.current = ids;
    // @ts-ignore
    tableDataRef.current = curTableLists;
    setTableData(curTableLists);

    // @ts-ignore
    generateTableDataTree(curTableLists, rowData);
    setRowData(rowData);
    itemArr.forEach((item) => {
      const curRef = headerMap.get(item.code!);
      curRef?.setValue(getResult(item.code!));
    });
  };

  const handleSelectCol = (key: string, value: boolean) => {
    const tableLists = (tableDataRef.current || tableData).slice();
    let ids: number[] = selectedIds.slice();
    let solvedIds: number[] = [];

    const curTableLists = tableLists.map((item) => {
      if (key in item && key === 'Query' && !value) {
        solvedIds = tableLists
          .map((listItem) => listItem.authority_details)
          .flat(1)
          .map((auth) => auth.id);
        return updateTableListItem(item, false);
      } else if ('Query' in item && key !== 'Query' && value && key in item) {
        solvedIds = markAuthIds(solvedIds, key, item);
        solvedIds = markAuthIds(solvedIds, 'Query', item);
        return { ...item, [key]: value, Query: true };
      } else if (key in item) {
        solvedIds = markAuthIds(solvedIds, key, item);
        return { ...item, [key]: value };
      } else {
        return item;
      }
    });
    tableDataRef.current = curTableLists;

    solvedIds = Array.from(new Set(solvedIds));

    // 处理选择行的数据
    if (!value) {
      ids = ids.filter((id) => !solvedIds.includes(id));
      setSelectedRows([]);
    } else {
      ids = Array.from(new Set(ids.concat(solvedIds)));

      const rows = judgeSelectedRow(curTableLists);
      if (rows.length === tableData.length) {
        setSelectedRows(rows.concat('root'));
      } else {
        setSelectedRows(rows.filter((item) => item !== 'root'));
      }
    }

    setSelectedIds(ids);
    selectedIdsRef.current = ids;

    //@ts-ignore
    updateTableData(curTableLists);
  };

  const onSelectAll = (nextValue: string[]) => {
    // 选中影响行
    const diffArr = diffList(nextValue, selectedRows);
    const isChecked = nextValue.length > selectedRows.length;

    const actionsArr = tableData
      .map((item) => item.authority_details)
      .flat(1)
      .map((item) => item.id);
    if (diffArr.includes('root') && isChecked) {
      const curTableLists = tableData.map((item) => {
        return updateTableListItem(item, true);
      });

      setSelectedIds(selectedIds.concat(actionsArr));
      selectedIdsRef.current = selectedIds.concat(actionsArr);
      updateTableData(curTableLists);
      setSelectedRows(curTableLists.map((item) => item.main_key!).concat('root'));
    } else if (diffArr.includes('root') && !isChecked) {
      const curTableLists = tableData.map((item) => {
        return updateTableListItem(item, false);
      });
      setSelectedIds(selectedIds.filter((item) => !actionsArr.includes(item)));
      selectedIdsRef.current = [];
      updateTableData(curTableLists);
      setSelectedRows([]);
    } else {
      handleSelectRow(diffArr, isChecked);
      setSelectedRows(nextValue);
    }
  };

  // ? 重新写
  const getRoleTreeData = async () => {
    setTreeLoading(true);
    const res = await Api.getPath({});
    if (res.code === 0) {
      if (Array.isArray(res.data)) {
        setRoleInfos(res.data);
        const data = JSON.parse(JSON.stringify(res.data));
        // ! 灵异， 不用res.data 而用data children拼不上去
        getTaleDataMap(data);
        setColumnsMap(actionMap);
        // const leftTreeData = getLeftTreeData(res.data)
        // setTreeData(callerArr(leftTreeData))
        setTreeData(res.data);
        setTreeLoading(false);
        return res.data;
      }
    }
    setTreeLoading(false);
  };

  const handleSelectTree = (key: string, authIds = selectedIds) => {
    if (key && authMap.has(key)) {
      const lists = authMap.get(key)!;
      // @ts-ignore
      const { tableList, selectedRows: rows } = updateTableDataList(lists, authIds);
      if (rows.length === tableList.length) {
        rows.unshift('root');
      }
      setSelectedRows(rows);
      updateTableData(tableList);
      computeCol([key]);
    }
  };

  // 根据ID获取已经勾选的全部选项
  const getSelectedAuthInfo = async (id: number) => {
    const update_authority_ids: number[] = [];

    const res = await Api.readItem({
      id: id,
    });
    if (res.code === 0) {
      if (res?.data?.authorities?.length) {
        for (const i of res.data?.authorities) {
          update_authority_ids.push(i.id);
        }
      }
    }
    setSelectedIds(update_authority_ids);
    selectedIdsRef.current = update_authority_ids;
    return update_authority_ids;
  };

  const initForm = async () => {
    const record = location.state as any;
    setId(record?.id);

    if (record?.id) {
      form.setFieldsValue({
        id: record.id,
        name: record.name,
        organization_name: record.organization_name,
        organization_id: record.organization_id,
        keyword: '',
      });
    }
  };

  const init = () => {
    const record = location.state as any;
    let type = '';
    switch (record?.type) {
      case 'User':
        type = 'USER';
        setTypeName('USER');
        break;
      default:
        type = 'USER';
        setTypeName('USER');
        break;
    }

    initForm();

    getRoleTreeData().then(async () => {
      if (record?.id) {
        const ids = await getSelectedAuthInfo(record.id);
        const type = form.getFieldValue('type') || '用户';
        switch (type) {
          case '用户':
            setDefaultSelectedKeys(['CENTER中心/档案/基础资料']);
            handleSelectTree('CENTER中心/档案/基础资料', ids);
            break;
        }
      } else {
        switch (type) {
          case 'USER':
            setDefaultSelectedKeys(['CENTER中心/档案/基础资料']);
            handleSelectTree('CENTER中心/档案/基础资料');
            break;
        }
      }
    });
  };

  const fitKeyword = (target: string, keywordArr: string[]) => {
    return keywordArr.some((keyword) => (target || '').indexOf(keyword) !== -1);
  };

  const fitRoleInfo = (data: RoleInfo, keywordArr: string[]) => {
    return data.authorities.some((item) => fitKeyword(item.name, keywordArr));
  };

  // const handleParent

  const handleSearchTree = (value: string) => {
    const valueArr = value.split(' ');
    const result: RoleInfo[] = [];

    // ? 要确定选中的是唯一的值只要确定选中的category_name只有一条就行，同时展开的目录递归推进栈就好了
    const selectedCategoryName: string[] = [];

    const selectedExpandKeys: string[] = [];

    const filterRoleInfo = (data: RoleInfo) => {
      const parentKey = data.parent_category_path;

      result.push(data);
      if (parentKey) {
        const target = roleInfos.find((item) => item.category_path === parentKey);
        if (target) {
          selectedExpandKeys.unshift(target.category_path);

          filterRoleInfo(target);
        }
      }
    };

    roleInfos.forEach((item) => {
      if (
        fitKeyword(item.category_name, valueArr) ||
        fitKeyword(item.category_path, valueArr) ||
        fitKeyword(item.parent_category_path, valueArr) ||
        fitRoleInfo(item, valueArr)
      ) {
        if (
          fitKeyword(item.category_name, valueArr) ||
          fitKeyword(item.category_path, valueArr) ||
          fitRoleInfo(item, valueArr)
        ) {
          selectedCategoryName.unshift(item.category_path);
          selectedExpandKeys.unshift(item.category_path);
        }
        filterRoleInfo(item);
      }
    });

    const lists = uniqBy(result, 'category_path');

    // const leftTreeData = getLeftTreeData(lists)

    // setTreeData(callerArr(leftTreeData))
    setTreeData(lists);

    if (selectedCategoryName.length === 1) {
      setDefaultSelectedKeys(selectedCategoryName);
      setExpandedKeys(selectedExpandKeys);
      handleSelectTree(selectedCategoryName[0]);
    }
  };

  const onSearch = debounce(handleSearchTree, 500);

  /**
   * ? 需求1: 当前点击的是查询的时候其他的权限都要取消, 即当前整行都应该取消选择
   * ? 需求2: 当前点击的非查询的时候需要把查询选上
   * ? 需求3: 选中表头当前列的时候需要全部选中当前列
   * ? 需求4: 选中当前选项判断全选
   * ? 需求4:
   *
   * @param {TableList} record
   * @param {string} key
   * @param {boolean} value
   */
  const handleCheckedItem = (record: TableList, key: string, value: boolean) => {
    const tableLists = tableData.slice();

    let ids: number[] = [];
    const curTableLists = tableLists.map((item) => {
      // 当前key是否是 ”Query“
      const isKeyEqualQuery = key === 'Query';
      // 当前record是否有”Query“
      const isHasQueryKey = 'Query' in item;
      // 当前record是否有 key
      const isHasCurrentKey = key in item;
      // 是否当前记录
      const isCurrentRecord = record.main_key === item.main_key;

      // 判断是否是勾选的key是否为查询，当前行全部取消勾选
      if (!value && isKeyEqualQuery && isCurrentRecord && isHasCurrentKey) {
        ids = ids.concat(item.authority_details.map((detail) => detail.id));

        return { ...updateTableListItem(item, false) };
      } else if (isCurrentRecord && isHasCurrentKey && isHasQueryKey) {
        const isQuerySelected = item['Query'];
        const target = tableLists.find((list) => list.name === item.parent_name);

        const authLists = tableLists.filter((list) => list.parent_name === record.parent_name && key in list);
        const selectedAuthList = authLists.filter((auth) => auth[key]);

        if (target) {
          if (authLists.length - selectedAuthList.length === 1) {
            if (key in target) {
              ids = markAuthIds(ids, key, item);

              target[key] = value;
            }
          }
        }

        if (!isQuerySelected && value) {
          ids = markAuthIds(ids, key, item);
          ids = markAuthIds(ids, 'Query', item);

          if (target) {
            target['Query'] = true;
          }

          return { ...item, [key]: value, Query: true };
        } else {
          ids = markAuthIds(ids, key, item);

          return { ...item, [key]: value };
        }
      } else if (isCurrentRecord && isHasCurrentKey) {
        const target = tableLists.find((list) => list.name === item.parent_name);
        const authLists = tableLists.filter((list) => list.parent_name === record.parent_name && key in list);
        const selectedAuthList = authLists.filter((auth) => auth[key]);

        if (target) {
          if ('Query' in target) {
            target['Query'] = true;
          }
          // 当前权限最后一个也要将父级权限选中
          if (authLists.length - selectedAuthList.length === 1) {
            if (key in target) {
              target[key] = value;
            }
          }
        }

        ids = markAuthIds(ids, key, item);

        return { ...item, [key]: value };

        // !勾选父级， 处理子集
      } else if (item.parent_name === record.name && isHasCurrentKey) {
        if (isKeyEqualQuery && !value) {
          ids = ids.concat(item.authority_details.map((detail) => detail.id));
          return { ...updateTableListItem(item, false) };
        } else if (isKeyEqualQuery && value) {
          ids = markAuthIds(ids, key, item);
          return { ...item, [key]: value };
        } else if (!isKeyEqualQuery && !value) {
          ids = markAuthIds(ids, key, item);
          return { ...item, [key]: value };
        } else {
          ids = markAuthIds(ids, key, item);
          return { ...item, [key]: value };
        }
      } else {
        return item;
      }
    });

    let curSelectedRows = selectedRows.slice();
    let curSelectedIds = selectedIds.slice();

    // 处理全选问题
    if (value) {
      // 单行处理
      const isSelectedAll = record.authority_details.every((item) => record[authTypes[item.action]]);
      if (isSelectedAll) {
        curSelectedRows = [...curSelectedRows, record.main_key!];
      }

      curSelectedIds = curSelectedIds.concat(ids);

      // 列处理
    } else {
      // 单行处理
      // const isSelectedAll = record.authority_details.filter(item => authTypes[item.action] in record).every(item => record[authTypes[item.action]]);
      // let curSelectedRows = selectedRows.slice();
      curSelectedRows = curSelectedRows.filter((item) => item !== record.main_key);
      curSelectedIds = curSelectedIds.filter((item) => !ids.includes(item));
    }

    const rows = judgeSelectedRow(curTableLists);

    if (rows.length === tableData.length) {
      setSelectedRows(rows.concat('root'));
    } else {
      setSelectedRows(rows.filter((item) => item !== 'root'));
    }

    setSelectedIds(curSelectedIds);
    selectedIdsRef.current = curSelectedIds;
    tableDataRef.current = curTableLists;

    setTableData(curTableLists);

    // @ts-ignore

    generateTableDataTree(curTableLists, rowData);

    setRowData(rowData);

    itemArr.forEach((item) => {
      const curRef = headerMap.get(item.code!);

      curRef?.setValue(getResult(item.code!));
    });
  };
  const tableRender = (item: any) => {
    switch (item?.code) {
      case 'name':
        item.render = (value: any, record: any) => {
          return (
            <div className="overwidth">
              <span>{record.name}</span>
            </div>
          );
        };
        break;
      case 'Query':
      case 'Edit':
      case 'Del':
      case 'Comf':
      case 'Recomf':
      case 'Pass':
      case 'Deny':
      case 'Copy':
      case 'Handle':
      case 'Pf':
      case 'Print':
      case 'Exp':
      case 'Imp':
      case 'firstAudit':
      case 'secondAudit':
      case 'lock':
      case 'unlock':
      case 'contract':
      case 'finish':
      case 'buildedStore':
      case 'allocate':
      case 'stop':
      case 'pay':
      case 'business':
      case 'receive':
      case 'assign':
      case 'downloadVoucher':
      case 'SECOND_AUDIT':
      case 'FIRST_AUDIT':
      case 'REJECT':
      case 'SHARE':
      case 'SUBMIT':
      case 'RUZHI':
      case 'applyForChange':
      case 'distribute':
      case 'bind':
      case 'viewMobile':
      case 'forward':
      case 'copyAdd':
      case 'sync':
      case 'confirmItem':
      case 'fh':
        item.render = (value: any, record: TableList) => {
          return record[item.code] == true || record[item.code] == false ? (
            // return(
            <div className="overwidth" style={{ paddingLeft: 8 }}>
              <XlbCheckbox
                key={record.main_key! + item.code}
                // defaultChecked={record[item.code]}
                checked={record[item.code]}
                className={styles.checkbox}
                onChange={(e: any) => handleCheckedItem(record, item.code, e.target.checked)}
              />
            </div>
          ) : (
            <></>
          );
        };
        break;
    }
  };

  const getResult = (key: string, lists?: TableList[]) => {
    const tableLists = (lists || tableDataRef.current || tableData).slice();

    const isSelected = tableLists.filter((item) => key in item).every((item) => item[key]);

    return isSelected;
  };

  //1.重新编译columns,添加Checkbox
  const getColumns = () => {
    return new Promise((resolve) => {
      setItemArr([]);
      // let obj = {}
      let value: any[] = [];
      columns.map((v: any, i: number) => {
        if (i > 0) {
          // const result = lieCheck(v.code)
          const isSelected = getResult(v.code);
          // obj[result.code] = result.state
          value.push({
            ...v,
            name: (
              <div>
                <TableHeaderCheckBox
                  defaultChecked={isSelected}
                  ref={(ref) => headerMap.set(v.code, ref)}
                  key={v.code + isSelected}
                  // checked={isSelected}
                  className={styles.checkbox}
                  onChange={(e) => handleSelectCol(v.code, e.target.checked)}
                >
                  {v.name}
                </TableHeaderCheckBox>
              </div>
            ),
          });
        } else {
          value.push(v);
        }
      });
      resolve(value);
    });
  };
  //1.1 头部按钮是否勾选
  //2.列全选点击事件
  if (itemArr.length) {
    itemArr.map((v: any) => tableRender(v));
  }

  // 保存
  const saveOrder = async () => {
    try {
      await form.validateFields();
    } catch (err: any) {
      throw err;
    }
    const name = form.getFieldValue('name')?.trim();
    if (!name || name == '') {
      XlbTipsModal({ tips: '请输入角色名称!' });
      return;
    }
    const data = {
      name: name,
      authority_ids: Array.from(new Set(selectedIds)),
      organization_id: form.getFieldValue('organization_id'),
    };
    setisLoading(true);
    // 新增
    const res = !preId ? await Api.addItem({ ...data }) : await Api.updateItem({ ...data, id: preId });

    if (res.code === 0) {
      setisLoading(false);
      message.success('操作成功');
      back('/file/roleManage');
    }
  };

  const getLists = async (data: keywordReqDTO) => {
    const res = await Api.getLeftTree(data);
    if (res.code === 0) {
      return res.data!;
    }
    return [] as NonNullable<typeof res.data>;
  };
  const handleDialogClick = async () => {
    const bool = await XlbTreeModal({
      width: 330,
      title: '选择组织',
      dataType: 'tree',
      request: getLists,
      fieldName: {
        parent_id: 'pid',
      },
    });
    if (bool) {
      const item = bool.filter(Boolean)![0];
      if (item && item.children.length) return message.error('仅支持选择最小组织');
      form.setFieldsValue({
        organization_name: item.name,
        organization_id: item.id,
      });
    }
  };

  useEffect(() => {
    init();
  }, []);

  useEffect(() => {
    const type = form.getFieldValue('type') || '用户';
    switch (type) {
      case '用户':
        computeCol(['CENTER中心/档案/基础资料']);
        break;
      default:
        break;
    }
  }, [columnsMap]);
  useEffect(() => {
    if (columns.length && rowData.length) {
      getColumns().then((res: any) => {
        const newColumns = res.map((v: any) => {
          return {
            ...v,
            width: 130,
            align: 'left',
          };
        });
        newColumns.push({
          name: '',
        });
        setItemArr(newColumns);
        newColumns.map((v: any) => tableRender(v));
      });
    }
  }, [columns, rowData]);
  //递归Key
  const SearchKey = (tree: any, key: any = []) => {
    for (const item of tree) {
      key.push(item.category_path + '');
      if (item.children && item.children.length > 0) {
        SearchKey(item.children, key);
      }
    }
    return key;
  };

  const pipeline = useTablePipeline({ components: { Checkbox: XlbCheckbox } })
    .input({ dataSource: rowData, columns: itemArr })
    .primaryKey('main_key')
    .use(
      features.treeMode({
        onChangeOpenKeys: () => {},
        indentSize: 20,
      }),
    )
    .use(
      features.treeSelect({
        clickArea: 'checkbox',
        tree: rowData,
        checkedStrategy: 'all',
        value: selectedRows,
        onChange: onSelectAll,
        rootKey: 'root',
        checkboxPlacement: 'start',
        checkStrictly: true,
      }),
    );

  const Cell = (props: CellProps) => {
    const { colIndex, tdProps } = props;
    const { children, className, ...rest } = tdProps;

    let comp = children;
    /** @fixme 此处判断可能会有问题 */

    if (React.isValidElement(children) && Object.keys(children?.props).includes('checked')) {
      comp = <XlbCheckbox {...children.props} />;
    }

    return (
      <td key={colIndex} className={className} {...rest}>
        {comp}
      </td>
    );
  };

  return (
    <div className={styles.right}>
      <Layout className={styles.layoutBg}>
        <Sider width={230}>
          <div className={styles.left_nav_main}>
            <XlbInput
              style={{ width: 209, margin: '12px 10px 0' }}
              value={filterName}
              placeholder="请输入关键字"
              onChange={(e) => {
                setfilterName(e.target.value);
                onSearch(e.target.value);
              }}
              allowClear
            />
            <XlbTree
              type="base"
              dataType="lists"
              onSelect={(e, selectedKeys) => {
                setDefaultSelectedKeys(selectedKeys);
                handleSelectTree(e?.category_path, selectedIdsRef.current);
              }}
              dataSource={treeData}
              treeConfig={{
                selectedKeys: defaultSelectedKeys,
              }}
              fieldName={{
                parent_id: 'parent_category_path',
                name: 'category_name',
                id: 'category_path',
              }}
              style={{ overflowY: 'hidden', paddingLeft: 0 }}
            />
          </div>
        </Sider>
        <Content>
          <div style={{ borderLeft: '1px solid #D8D8D8' }}>
            <div className={'button_box row-flex'} style={{ paddingTop: 12 }}>
              <Form autoComplete="off" layout="inline" colon form={form}>
                <Form.Item label="角色名称" name="name" rules={[{ required: true, message: '' }]}>
                  <XlbInput style={{ width: 180 }} allowClear />
                </Form.Item>
                <Form.Item label="所属组织" name="organization_name" rules={[{ required: true, message: '' }]}>
                  <XlbInput
                    style={{ width: 180, borderRadius: 5 }}
                    readOnly
                    onClick={handleDialogClick}
                    suffix={<XlbIcon name="sousuo" size={14} />}
                  />
                </Form.Item>
              </Form>
            </div>
            <div className={styles.button_box}>
              <XlbButton.Group>
                {hasAuth(['角色管理', '编辑']) ? (
                  <XlbButton type="primary" label="保存" onClick={saveOrder} icon={<XlbIcon name="baocun" />} />
                ) : null}
                <XlbButton
                  type="primary"
                  label="返回"
                  onClick={() => back('/file/roleManage', false)}
                  icon={<XlbIcon name="fanhui" />}
                />
              </XlbButton.Group>
            </div>
          </div>
          <div className={styles.table_fold_box}>
            <BaseTable
              isLoading={isLoading}
              className="jMuPhs"
              style={{
                ...tableStyle,
                overflow: 'auto',
                height: document.body.clientHeight - 192,
              }}
              emptyCellHeight={document.body.clientHeight - 225}
              {...pipeline.getProps()}
              components={{
                Cell: Cell,
              }}
            />
          </div>
        </Content>
      </Layout>
    </div>
  );
};

export default RoleManageItem;
