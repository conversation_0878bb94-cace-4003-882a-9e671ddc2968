import React, { FC, useState, } from 'react'
import { message } from 'antd'
import Api from './service'
import { ContextState, XlbBasicForm, XlbButton, XlbForm, XlbPageContainer, XlbTableColumnProps, XlbTipsModal, SearchFormType, XlbIcon as IconFont, XlbDrawer } from '@xlb/components'
import { PageReqDTO } from './type/request'
import styles from './index.module.less'
import { hasAuth } from '@/utils'
import exportFile from '@xlb/utils/dist/src/utils/downloadBlobFile'
import AssociationDetails from "@/pages/goodsFile/index/component/AssociationDetails"
import { useKeepAliveRefresh } from '@/hooks'
const { Table, ToolBtn, SearchForm } = XlbPageContainer
const Index: FC = () => {
    const [form] = XlbBasicForm.useForm()
    const { go } = useKeepAliveRefresh()
    const [isLoading, setIsLoading] = useState<boolean>(false)
    const [drawerOpen, setDrawerOpen] = useState<boolean>(false)
    const [drawerItem, setDrawerItem] = useState<any>(undefined)
    const [handleDetails, setHandleDetails] = useState<any>(undefined)
    const [editFlag, setEditFlag] = useState<boolean>(false)
    let refresh: any = () => { }
    const getCateGory = async (data: { keyword: string }) => {
        const res = await Api.getCategory(data)
        if (res.code === 0) {
            return res.data!
        }
        return [] as NonNullable<typeof res.data>
    }
    const prevPost = () => {
        const formData = form.getFieldsValue(true)
        const data = new PageReqDTO(formData)
        return data
    }
    const handleBind = async (selectRow: any[], fetchData: Function) => {
        const bool = await XlbTipsModal({
            title: '批量解绑',
            tips: `是否确定要将“${selectRow?.map(item => item.name).join(',')}”的商品解除绑定关系`,
            isCancel: true,
        })
        if (bool) {
            //调用解绑接口进行解绑
            const { code } = await Api.batchUnbind({ item_ids: selectRow?.map(item => item.id) })
            if (code === 0) {
                message.success('操作成功')
                fetchData()
            }
        }
    }
    const tableList: XlbTableColumnProps<any>[] = [
        {
            name: '序号',
            code: '_index',
            width: 60,
            align: 'center'
        },
        {
            name: '中心商品名称',
            code: 'name',
            width: 150,
            render: (text) => (
                <div className={styles.overwidth}>{text}</div>
            )
        },
        {
            name: '绑定明细',
            code: 'companies',
            width: 100,
            render: (text, record) => (
                <div onClick={e => {
                    e.stopPropagation()
                    setDrawerItem(record)
                    setDrawerOpen(true)
                }} className={styles.overwidth + " link"}>
                    已绑定({text?.length || 0})
                </div>
            )
        },
        {
            name: '商品条码',
            code: 'bar_code',
            width: 100,
            render: (text, record) => (
                <div
                    onClick={(e) => {
                        e.stopPropagation()
                        go('/file/goodsFile/item', {
                            state: {
                                record: {
                                    ...record,
                                    type: 'edit',
                                },
                                refresh: refresh,
                                url: location.pathname,
                            },
                        })
                    }}
                    className={styles.overwidth + ' link'}>{text}</div>
            )
        },
        {
            name: '商品代码',
            code: 'code',
            width: 100,
            render: (text) => (
                <div className={styles.overwidth}>{text}</div>
            )
        },
        {
            name: '商品品牌',
            code: 'item_brand',
            width: 100,
            render: (value) => (
                <div className={styles.overwidth}>{value?.name}</div>
            )
        },
        {
            name: '一级分类',
            code: 'one_category_name',
            width: 120,
            render: (text) => (
                <div className={styles.overwidth}>{text}</div>
            )
        },
        {
            name: '二级分类',
            code: 'two_category_name',
            width: 120,
            render: (text) => (
                <div className={styles.overwidth}>{text}</div>
            )
        },
        {
            name: '三级分类',
            code: 'three_category_name',
            width: 120,
            render: (text) => (
                <div className={styles.overwidth}>{text}</div>
            )
        },
        {
            name: '采购规格',
            code: 'purchase_spec',
            width: 120,
            render: (text) => (
                <div className={styles.overwidth}>{text}</div>
            )
        },
        {
            name: '基本单位',
            code: 'unit',
            width: 90,
            render: (text) => (
                <div className={styles.overwidth}>{text}</div>
            )
        },
        {
            name: '采购单位',
            code: 'purchase_unit',
            width: 90,
            render: (text) => (
                <div className={styles.overwidth}>{text}</div>
            )
        },
        {
            name: '采购换算率',
            code: 'purchase_ratio',
            width: 100,
            render: (text) => (
                <div className={styles.overwidth}>{text}</div>
            ),
            align: 'right'
        },
        {
            name: '商品类型',
            code: 'item_type_name',
            width: 120,
            render: (text) => (
                <div className={styles.overwidth}>{text}</div>
            )
        },
        {
            name: '停购',
            code: 'stop_purchase',
            width: 90,
            render: (text) => (
                <div className={styles.overwidth}>{text ? '是' : '否'}</div>
            )
        },
        {
            name: '停售',
            code: 'stop_sale',
            width: 90,
            render: (text) => (
                <div className={styles.overwidth}>{text ? '是' : '否'}</div>
            )
        },
        {
            name: '停止要货',
            code: 'stop_request',
            width: 90,
            render: (text) => (
                <div className={styles.overwidth}>{text ? '是' : '否'}</div>
            )
        },
        {
            name: '停止批发',
            code: 'stop_wholesale',
            width: 90,
            render: (text) => (
                <div className={styles.overwidth}>{text ? '是' : '否'}</div>
            )
        },
    ]
    const formList: SearchFormType[] = [
        {
            label: '关键字',
            name: 'keyword',
            type: 'input',
            width: 200,
            allowClear: true,
            placeholder: '请输入商品条码、名称',
        },
        {
            label: "商品分类",
            name: "category_ids",
            type: "inputDialog",
            allowClear: true,
            multiple: true,
            maxLength: 999999999,
            treeModalConfig: {
                title: '商品分类',
                width: 300,
                dataType: 'tree',
                checkable: true,
                fieldName: { parent_id: 'pid' },
                request: getCateGory
            },
            placeholder: '',
        },
        {
            label: '商品品牌',
            name: 'item_brand_ids',
            type: 'select',
            allowClear: true,
            multiple: true,
            selectRequestParams: {
                url: "/center/hxl.center.itembrand.find",
                responseTrans: {
                    label: "name",
                    value: "id",
                },
            },
            placeholder: '',
            showSearch: true,
            filterOption: (input, option) => (option?.label ?? '').includes(input),
        },
        {
            label: '关联公司',
            name: 'company_id',
            type: 'select',
            placeholder: '',
            options: [],
            selectRequestParams: {
                url: "/center/hxl.center.company.find",
                responseTrans: {
                    label: "name",
                    value: "id",
                },
            },
        },
    ]
    //导入
    //   const handleImport = async () => {
    //     const res = await XlbImportModal({
    //       importUrl: `${baseURL}/center/hxl.center.item.association.category.import`,
    //       templateUrl: `${baseURL}/center/hxl.center.item.association.category.download`,
    //     })
    //     if (res.code === 0) {
    //       if (res.data?.state) {
    //         message.success('操作成功')
    //         refresh()
    //       } else {
    //         XlbTipsModal({ tips: '错误信息：', tipsList: res.data.error_messages })
    //       }
    //     }
    //   }
    //导出
    const handleExport = async () => {
        const postData = prevPost()
        setIsLoading(true)
        const { status, data } = await Api.exportItems(postData)
        setIsLoading(false)
        if (status === 200 && data) {
            exportFile('商品档案关联导出', data)
        }
    }
    const handleDetailsFun = async () => {
        //调用接口保存绑定明细
        //接口调用成功关闭弹窗否则不关闭防止因为网络或者其他原因发生数据未保存弹窗被关闭的情况
        const { code } = await Api.batchBind({
            item_id: drawerItem.id,
            item_infos: handleDetails.map((item: any) => ({
                ...item,
                erp_item_id: item.id
            })),
            operation_content: [
                {
                    business_code: drawerItem.code,
                    business_name: drawerItem.name,
                    business_scope: handleDetails.map((item: any) => item.company_name).join(',')
                }
            ]
        })
        if (code === 0) {
            message.success('操作成功')
            setDrawerOpen(false)
            refresh()
        }
    }
    const closeDrawer = () => {
        setDrawerOpen(false)
        refresh()
    }
    return (
        <XlbPageContainer
            url={'/center/hxl.center.item.association.page'}
            prevPost={prevPost}
            tableColumn={tableList}
            immediatePost
        >
            <SearchForm>
                <XlbForm formList={formList} form={form} isHideDate={true} getFormRecord={() => refresh()} />
            </SearchForm>
            <ToolBtn showColumnsSetting={false}>
                {({ selectRow, selectRowKeys, fetchData, loading, dataSource }: ContextState<any>) => {
                    refresh = fetchData
                    return (
                        <XlbButton.Group>
                            <XlbButton
                                type="primary"
                                label="查询"
                                loading={loading}
                                onClick={() => { fetchData() }}
                                icon={<IconFont name="sousuo" color="currentColor" size={16} />}
                            />
                            {/* {hasAuth(['商品档案关联', '导入']) && (
                                <XlbButton
                                    type="primary"
                                    label="导入"
                                    disabled={loading}
                                    onClick={() => handleImport()}
                                    icon={<IconFont name="daoru" color="currentColor" size={16} />}
                                />
                            )} */}
                            {hasAuth(['商品档案关联', '导出']) && (
                                <XlbButton
                                    type="primary"
                                    label="导出"
                                    disabled={!dataSource?.length}
                                    loading={isLoading}
                                    onClick={() => handleExport()}
                                    icon={<IconFont name="daochu" color="currentColor" size={16} />}
                                />
                            )}
                            {/* {
                                hasAuth(['商品档案关联', '绑定']) && <XlbButton
                                    type="primary"
                                    label="批量解绑"
                                    disabled={!selectRowKeys?.length || loading}
                                    onClick={() => handleBind(selectRow, fetchData)}
                                    icon={<IconFont name="jiebang" color="currentColor" size={16} />}
                                />
                            } */}
                        </XlbButton.Group>
                    )
                }}
            </ToolBtn>
            <Table selectMode='multiple' />
            <XlbDrawer
                title={'绑定明细'}
                placement={'right'}
                width={1000}
                onClose={() => setDrawerOpen(false)}
                destroyOnClose={true}
                open={drawerOpen}
                maskClosable={false}
                footer={
                    <div className={styles.AssociationDetailsFlex} style={{ width: '100%', justifyContent: "right" }}>
                        <XlbButton onClick={() => setDrawerOpen(false)} type="line">关闭</XlbButton>&nbsp;&nbsp;
                        <XlbButton onClick={() => editFlag ? handleDetailsFun() : setDrawerOpen(false)}>确认</XlbButton>
                    </div>
                }
            >
                <AssociationDetails setDetails={value => {
                    setHandleDetails(value)
                    setEditFlag(true)
                }} item={drawerItem} closeDrawer={closeDrawer} />
            </XlbDrawer>
        </XlbPageContainer>
    )
}
export default Index