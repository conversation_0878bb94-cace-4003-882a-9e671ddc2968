@import 'styles/alitable';
@import 'assets/iconfont/iconfont.css';

@font-face {
  font-family: 'NUM';
  src: local('helvetica'), local('arial'), local('verdana'), local('sans-serif');
  unicode-range: U+30-39, U+61-7a, U+41-5a;
}

html {
  overflow: hidden;
}

body {
  margin: 0;
  padding: 0;
}

// 通用样式
html #root-master {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: auto;
  font-weight: 400;
  font-size: 13px;
  font-family: 'NUM', 'PingFang SC', helvetica, arial, verdana, sans-serif;
  background-color: @color_fff;
}

//::-webkit-scrollbar {
//  width: 0;
//  height: 6px;
//}
//
//::-webkit-scrollbar-thumb {
//  background-color: #999;
//  border-radius: 3px;
//}
//
//::-webkit-scrollbar-track {
//  // box-shadow: inset 0 0 6px rgba(153, 153, 153, 0.5);
//  box-shadow: inset 0 0 6px transparent;
//  background: transparent;
//}

// type="number" inputmode="decimal"去掉上下箭头
/*谷歌*/
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

/*火狐*/
input[type="number"] {
  -moz-appearance: textfield;
}

p {
  margin: 0;
}

.container {
  height: calc(100vh - 78px);
  overflow: auto;
}

// 常用样式
.border {
  border: 1px solid red;
}

.v-box5 {
  height: @size_5;
}

.v-box10 {
  height: @size_10;
}

.h-box5 {
  width: @size_5;
}

.h-box10 {
  width: @size_10;
}

.cursors {
  cursor: pointer;
}

.full-box {
  width: 100%;
  height: 100%;
}

.v_padding10 {
  padding: @size_10 0 0 0;
}

.top-margin10 {
  margin-top: @size_10;
}

.top-margin12 {
  margin-top: @size_12;
}

.top-margin14 {
  margin-top: @size_14;
}

.top-margin16 {
  margin-top: @size_16;
}

.top-margin18 {
  margin-top: @size_18;
}

.top-margin20 {
  margin-top: @size_20;
}

.left-margin10 {
  margin-left: @size_10;
}

.left-margin12 {
  margin-left: @size_12;
}

.left-margin14 {
  margin-left: @size_14;
}

.left-margin16 {
  margin-left: @size_16;
}

.left-margin20 {
  margin-left: @size_20;
}

.right-margin10 {
  margin-right: @size_10;
}

.right-margin12 {
  margin-right: @size_12;
}

.right-margin14 {
  margin-right: @size_14;
}

.right-margin16 {
  margin-right: @size_16;
}

.right-margin18 {
  margin-right: @size_18;
}

.right-margin20 {
  margin-right: @size_20;
}

// flex布局样式
.row-flex {
  display: flex;
  flex-direction: row;
}

.flex-wrap {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.col-flex {
  display: flex;
  flex-direction: column;
}

.h-flex {
  display: flex;
  justify-content: center;
}

.v-flex {
  display: flex;
  align-items: center;
}

.c-flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

.link {
  color: @color_link;
  text-decoration: underline;
  cursor: pointer;
}

.info {
  color: @color_init;
}

.default {
  color: @color_link;
}

.warning {
  color: @color_warning;
}

.success {
  color: @color_success;
}

.danger {
  color: @color_danger;
}

.invalid {
  color: @color_invalid;
}

.purple {
  color: @color_purple;
}

.default_bg {
  background-color: @color_default;
}

.warning_bg {
  background-color: @color_warning;
}

.success_bg {
  background-color: @color_success;
}

.danger_bg {
  background-color: @color_danger;
}

.purple_bg {
  background-color: @color_purple;
}

.theme {
  color: @color_theme;
}

// 蓝色横条样式
.tabBlock {
  display: flex;
  align-items: center;
  height: 25px;
  padding: 0 10px;
  color: @color_fff;
  background: @color_theme;
}

// 左侧分类样式
.leftGoods {
  float: left;
  width: 230px;
  height: 100%;
  overflow: hidden;
  background: #fff;
  border-right: 1px solid #ccc;
}

//右侧分类样式
.ant-layout-content {
  background-color: #fff;
}

.left_context {
  margin-top: 5px;
  margin-left: 10px;
}