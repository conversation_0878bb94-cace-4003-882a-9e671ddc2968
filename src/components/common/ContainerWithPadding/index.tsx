import type { CSSProperties, FC, PropsWithChildren } from 'react';
import classnames from 'classnames';
import styles from  './index.module.less';

interface XlbItemContainerProps {
  containerStyle?: CSSProperties
  containerClassName?: string
  contentBodyStyle?: CSSProperties
  contentBodyClassName?: string
}

const ContainerWithPadding: FC<PropsWithChildren<XlbItemContainerProps>> = ({
  children,
  containerClassName,
  containerStyle,
}) => {
  return (
    <div style={containerStyle} className={classnames(styles.containerWithPadding, containerClassName)}>
      {children}
    </div>
  )
}

export default ContainerWithPadding;
