const getOptionRequest = {
  async request(formValues: any, anybaseURL?: any, globalFetch?: any) {
    const res = await globalFetch.post(anybaseURL + '/mdm/manage.userlog.select.list', {
      ...formValues,
    });
    if (res.code === 0) {
      return res.data.map((item: any) => ({
        label: item,
        value: item,
      }));
    }
    return [];
  },
};

export const searchFormList = [
  { id: 'dateCommon', label: '时间范围', name: 'create_date' },
  {
    label: '操作平台',
    name: 'source',
    componentType: 'select',
    tag: 'center',
    id: 'operationTypeList',
    params: {
      module: 'user_log',
      type: 'OA',
      category: 'source',
    },
    getOptionRequest,
  },
  {
    id: 'userIds',
    label: '操作员',
    name: 'user_name',
    componentType: 'inputDialog',
    tag: 'center',
    fieldProps: {
      fieldNames: {
        idKey: 'name',
        nameKey: 'name',
      },
      dialogParams: {
        isLeftColumn: false,
        type: 'UserForOA',
        dataType: 'tree',
        isMultiple: true,
      },
    },
  },
  { id: 'commonInput', label: '操作日志', name: 'memo' },
  { id: 'commonInput', label: '操作项', name: 'item' },
  {
    label: '操作动作',
    name: 'action',
    componentType: 'select',
    tag: 'center',
    id: 'operationTypeList',
    params: {
      module: 'user_log',
      type: 'OA',
      category: 'action',
    },
    getOptionRequest,
  },
];
