import { type FC, type PropsWithChildren } from 'react';
import { XlbProPageContainer } from '@xlb/components';
import { searchFormList } from './data';
import dayjs from 'dayjs';

interface XlbUserOperationLogProps {
  tableColumn?: any;
  system_type?: string;
  fileName?: string;
}

const UserOperationLog: FC<PropsWithChildren<XlbUserOperationLogProps>> = ({ tableColumn, system_type, fileName }) => {
  return (
    <XlbProPageContainer
      searchFieldProps={{
        formList: searchFormList,
        initialValues: {
          create_date: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
          type: 'OA',
          system_type: system_type,
          company_id: 1000,
        },
      }}
      tableFieldProps={{
        url: '/mdm/manage.userlog.page',
        tableColumn: tableColumn,
        immediatePost: true,
        showColumnsSetting: true,
      }}
      exportFieldProps={{
        url: '/mdm/manage.userlog.export',
        fileName: fileName,
      }}
    />
  );
};

export default UserOperationLog;
