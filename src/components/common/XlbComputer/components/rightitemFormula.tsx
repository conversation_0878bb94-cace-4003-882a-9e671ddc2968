import { XlbIcon, XlbInputNumber, XlbSelect } from '@xlb/components';
import classnames from 'classnames';
import React, { Fragment } from 'react';
import addGray from '@/assets/public/addGray.png';
import addBlue from '@/assets/public/addblue.png';
import type { FormulaProps } from '../index';
import { Box } from './Box';
import { Dustbin } from './Dustbin';
import { ItemTypes } from './ItemTypes';
import ExpandContent from './expandcontent';
import { DragTagItem } from './leftItem';
import type { RightItemProps } from './rightitem';
export interface RightItemConditionProps extends RightItemProps {}

interface SourceDataProps {
  fieldBoxData: DragTagItem[];
  sourceData: FormulaProps[];
  onDeleteFormulaItem: (idxFormulaItem: number) => void;
  onAddConditionItem: () => void;
  clearAllTag: (idxFormulaItem: number) => void;
  handleDel: (idx: number, name: string, idxFormulaItem: number) => void;
  handleDrop: (item: DragTagItem, idxFormulaItem: number) => void;
  moveBox: (dragIndex: number, hoverIndex: number, idxFormulaItem: number) => void;
  handleChangeNumber: (
    value: number | undefined,
    index: number,
    boxData: DragTagItem[],
    idxFormulaItem: number,
  ) => void;
  handleSetCustField: (index: number, name: string, idxFormulaItem: number) => void;

  changeJudgeField: (value: string | undefined, idxFormulaItem: number) => void;
  changeJudgeSymbol: (value: '>' | '<' | '>=' | '<=' | '=', idxFormulaItem: number) => void;
  changeJudgeValue: (value: undefined | number, idxFormulaItem: number) => void;
  maxFormulaCount?: number;
  isDisableAll?: boolean;
}

export default (props: Partial<SourceDataProps>) => {
  const {
    onDeleteFormulaItem,
    sourceData,
    handleDrop,
    handleDel,
    moveBox,
    handleChangeNumber = (value, index) => {},
    handleSetCustField = (index, name) => {},
    onAddConditionItem,
    fieldBoxData,
    clearAllTag,
    changeJudgeField,
    changeJudgeSymbol,
    changeJudgeValue,
    maxFormulaCount,
    isDisableAll,
  } = props;
  const renderCard = (v: DragTagItem, index: number, idxFormulaItem: number) => {
    const { boxData, controlerDisabled, tag_type } = v;
    return (
      <Fragment key={index}>
        <Box
          controlerDisabled={controlerDisabled}
          state={boxData}
          itemData={v}
          className={classnames('right-item-tag', tag_type === 'symbol' ? tag_type : '')}
          index={index}
          // @ts-ignore
          handleDel={(...rest) => handleDel(...rest, idxFormulaItem)}
          // @ts-ignore
          moveBox={(...rest) => moveBox(...rest, idxFormulaItem)}
          key={v?.id}
          name={
            v?.customized_field && v.tag_type === 'control' ? (
              <XlbInputNumber
                disabled={!!controlerDisabled}
                style={{ width: 80 }}
                controls
                value={v?.value}
                onChange={(value) => handleChangeNumber(value as number | undefined, index, boxData, idxFormulaItem)}
              />
            ) : v?.customized_field && v.tag_type === 'field' ? (
              <span onClick={() => handleSetCustField(index, v.name, idxFormulaItem)}>{v.name}</span>
            ) : (
              <>{v.name}</>
            )
          }
          id={v?.id}
          renderkey={v.id}
          boxType={ItemTypes.EXCHANGE}
        />
      </Fragment>
    );
  };
  return (
    <div className="right-item">
      {sourceData?.map((v, i: number) => {
        const {
          boxData,
          field,
          judgeSymbol,
          judgeValue,
          isJudgeValueDisabled,
          isJudgeSymbolDisabled,
          isFieldDisabled,
        } = v;
        return (
          <Fragment key={i}>
            <ExpandContent
              onDelete={() => (onDeleteFormulaItem ? onDeleteFormulaItem(i) : '')}
              formulaIndex={i}
              isDisabled={isDisableAll}
              content={
                <>
                  <div
                    style={{
                      width: '100%',
                      display: 'flex',
                      gap: '8px',
                      margin: '12px 0',
                    }}
                  >
                    <XlbSelect
                      disabled={!!isFieldDisabled}
                      allowClear
                      placeholder="请选择字段"
                      onChange={(value) => (changeJudgeField ? changeJudgeField(value, i) : {})}
                      style={{ flex: 2 }}
                      value={field}
                      options={fieldBoxData
                        ?.filter((v) => !v?.hiddenRight)
                        ?.map((v) => ({
                          label: v.name,
                          value: v.name,
                        }))
                        ?.filter((item) => item.label !== '自定义字段')}
                    />
                    <XlbSelect
                      disabled={!!isJudgeSymbolDisabled}
                      allowClear
                      placeholder="判断符"
                      onChange={(value) => (changeJudgeSymbol ? changeJudgeSymbol(value, i) : {})}
                      style={{ flex: 1 }}
                      value={judgeSymbol}
                      options={[
                        { label: '大于', value: '>' },
                        { label: '小于', value: '<' },
                        { label: '大于等于', value: '>=' },
                        { label: '小于等于', value: '<=' },
                        { label: '等于', value: '=' },
                      ]}
                    />
                    <XlbInputNumber
                      disabled={!!isJudgeValueDisabled}
                      step={0.1}
                      precision={2}
                      controls
                      placeholder="请输入内容"
                      style={{ flex: 2 }}
                      value={judgeValue}
                      onChange={(value) => {
                        // @ts-ignore
                        changeJudgeValue ? changeJudgeValue(value, i) : {};
                      }}
                    />
                  </div>
                  {
                    <Dustbin
                      // @ts-ignore
                      clearAllTag={(...rest) => clearAllTag(...rest, i)}
                      text={'补贴金额='}
                      state={boxData}
                      // @ts-ignore
                      handleDrop={(...rest) => handleDrop(...rest, i)}
                    >
                      {boxData.length === 0 ? (
                        <span className="placeholder-tip">拖拽字段/符号/控件到这里</span>
                      ) : (
                        boxData?.map((v, index) => {
                          return renderCard(v, index, i);
                        })
                      )}
                    </Dustbin>
                  }
                </>
              }
            />
          </Fragment>
        );
      })}
      <div className="add-condition">
        {isDisableAll ? <div className="add-condition-mask"></div> : null}
        <span onClick={onAddConditionItem} style={{ display: 'inline-block', paddingRight: 8 }}>
          <img
            className="icon-jia"
            src={sourceData?.length === maxFormulaCount ? addGray : addBlue}
            style={{ width: 16, height: 16, display: 'inline-block' }}
          />
          <span
            className="add"
            style={
              sourceData?.length === maxFormulaCount
                ? { color: '#C9CDD4', cursor: 'not-allowed', paddingLeft: 22 }
                : { color: '#3D66FE', paddingLeft: 22 }
            }
          >
            添加条件
          </span>{' '}
        </span>
        <span className="tips">
          ( <span style={{ color: '#F53F3F' }}>*</span>
          添加条件在兑现周期内，满足条件才可以生成兑现单；满足多个条件时，优先第一个条件公式计算。)
        </span>
      </div>
    </div>
  );
};
