import { XlbIcon } from "@xlb/components";
import React, { FC, ReactNode, useState } from "react";
import "./expandcontent.less";
interface ExpandContentProps {
  formulaIndex: number; // 条件公式索引
  content: ReactNode;
  onDelete: (index: number) => void; // 删除
  isDisabled: boolean; // 是否禁用
}
const ExpandContent: FC<Partial<ExpandContentProps>> = (props) => {
  const { formulaIndex = 0, content, onDelete, isDisabled } = props;
  const [isActive, setIsActive] = useState(true);
  return (
    <div className="xlb-expand-content" style={{ height: isActive ? "auto" : "32px" }}>
      {isDisabled ? <div className="xlb-expand-content-mask"></div> : null}
      <div className="operate">
        <div
          onClick={() => setIsActive(!isActive)}
          style={
            !isActive
              ? { marginRight: 8 }
              : { marginRight: 8, transform: "rotate(180deg)" }
          }
        >
          <XlbIcon name="zhankai" width={16} height={16} color="#86909C" />
        </div>
        <div>条件公式{formulaIndex + 1}</div>
        <div
          className="del"
          style={{ display: formulaIndex === 0 ? "none" : "block" }}
          onClick={() => (onDelete ? onDelete(formulaIndex) : {})}
        >
          <XlbIcon name="shanchu" width={16} height={16} color={"#3d66fe"} />
        </div>
      </div>
      <div className="content" >
        {content}
      </div>
    </div>
  );
};
export default ExpandContent;
