.xlb-expand-content * {
  box-sizing: content-box;
}
.xlb-expand-content {
  background: #FFFFFF;
  border: 1px solid #E5E6EA;
  margin-bottom: 12px;
  transition: all 1s ease-out;
  overflow: hidden;
  position: relative;
  .xlb-expand-content-mask {
    position: absolute;
    width: 100%;
    height: 100%;
    inset: 0;
    z-index: 999;
    opacity: 0;
  }
  .operate {
    font-weight: 500;
    cursor: pointer;
    padding: 6px 11px;
    font-size: 14px;
    color: #1D2129;
    height: 20px;
    background: #F2F3F5;
    display: flex;
    justify-content: start;
    align-items: center;
    position: relative;
    .del {
      position: absolute;
      right: 11px;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
    }
  }
  .content {
    overflow: hidden;
    white-space: normal;
    word-break: break-all;
    padding: 0px 11px 16px 11px;
  }
}
