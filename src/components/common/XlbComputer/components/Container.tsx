import { SearchFormType, XlbBasicForm, XlbForm, XlbModal } from '@xlb/components';
import update from 'immutability-helper';
import cloneDeep from 'lodash/cloneDeep';
import React, {
  ForwardRefRenderFunction,
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { FormulaProps, XlbComputerProps, XlbComputerRefProps } from '../index';
import LeftItem, { DragTagItem } from './leftItem';
import RightItem from './rightitem';
import RightItemFormula from './rightitemFormula';
import { controlBoxData, evaluateExpression, fieldBoxDataDefault, renameKeysInObject, symbolBoxData } from './util';

const Container: ForwardRefRenderFunction<XlbComputerRefProps, XlbComputerProps> = function Container(
  {
    fieldBoxData = fieldBoxDataDefault,
    isConditionFormula = false,
    onChange,
    maxFormulaCount,
    isDisableAll = false,
    fieldList,
    initboxData,
  },
  ref,
) {
  const [conditionFormulaData, setConditionFormulaData] = useState<FormulaProps[]>([
    {
      boxData: [],
    },
  ]);

  const [boxData, setBoxData] = useState<DragTagItem[]>([]);
  // 删除时state
  const [delPopVisible, setDelPopVisible] = useState<boolean>(false);
  const [currentName, setCurrentName] = useState<React.ReactNode>('');
  const [currentIdx, setCurrentIdx] = useState<number>(0);
  const [currentIdxFormulaItem, setCurrentIdxFormulaItem] = useState<number>(0);

  // 自定义字段时候
  const [custFieldVisible, setCustFieldVisible] = useState<boolean>(false);

  const [form] = XlbBasicForm.useForm();
  const formList: SearchFormType[] = [
    {
      type: 'input',
      label: '字段名称',
      name: 'fieldKey',
      width: 264,
      rules: [{ required: true, message: '请输入' }],
    },
  ];

  const handleDrop = (item: DragTagItem) => {
    const { customized_field, tag_type, index, name, formulaTemplate } = item;
    setBoxData((prev: DragTagItem[]) => {
      let prevCopy = cloneDeep([...prev, item]);
      // 公式模板
      if (Array.isArray(formulaTemplate) && formulaTemplate.length > 0) {
        prevCopy = [...prev, ...formulaTemplate];
      }
      onChange && onChange(prevCopy);
      return prevCopy;
    });
    // 自定字段drop后打开弹窗
    if (customized_field && tag_type === 'field') {
      // 模板的时候打开自定义字段 索引是 boxData.length + item.formulaTemplate.length 减去 两边括弧 2
      // name 是 自定义字段
      if (Array.isArray(formulaTemplate) && formulaTemplate.length > 0) {
        handleSetCustField(boxData.length + formulaTemplate.length - 2, '自定义字段');
      } else {
        handleSetCustField(boxData.length, name);
      }
    }
  };

  const handleDropFormula = (item: DragTagItem, idxFormulaItem: number) => {
    const { customized_field, tag_type, index, name, formulaTemplate } = item;
    setConditionFormulaData((prevState) => {
      const copyPreState = cloneDeep(prevState);
      // 条件公式带模板
      if (Array.isArray(formulaTemplate) && formulaTemplate.length > 0) {
        copyPreState[idxFormulaItem].boxData = [...copyPreState[idxFormulaItem].boxData, ...formulaTemplate];
      } else {
        copyPreState[idxFormulaItem].boxData = [...copyPreState[idxFormulaItem].boxData, item];
      }
      onChange && onChange(copyPreState);
      return copyPreState;
    });
    // 自定字段drop后打开弹窗
    if (customized_field && tag_type === 'field') {
      const len = conditionFormulaData[idxFormulaItem].boxData.length;
      // 条件公式带模板的时候打开自定义字段 索引是 conditionFormulaData[idxFormulaItem].boxData.length + item.formulaTemplate.length 减去 两边括弧 2
      if (Array.isArray(formulaTemplate) && formulaTemplate.length > 0) {
        handleSetCustFieldFormula(len + formulaTemplate.length - 2, '自定义字段', idxFormulaItem);
      } else {
        handleSetCustFieldFormula(len, name, idxFormulaItem);
      }
    }
  };

  const handleDel = (idx: number, name: React.ReactNode) => {
    // setDelPopVisible(true);
    // setCurrentName(name);
    // setCurrentIdx(idx);
    setBoxData((prevState) => {
      const copyBoxData = cloneDeep(prevState);
      copyBoxData.splice(idx, 1);
      onChange && onChange(copyBoxData);
      return copyBoxData;
    });
  };

  const clearAllTag = () => {
    setBoxData([]);
    onChange && onChange([]);
  };

  const clearAllTagFormula = (idxFormulaItem: number) => {
    setConditionFormulaData((prevState) => {
      const copyPreState = cloneDeep(prevState);
      copyPreState[idxFormulaItem].boxData = [];
      return copyPreState;
    });
  };

  useEffect(() => {
    if (isConditionFormula) {
      setConditionFormulaData([
        {
          boxData: [],
        },
      ]);
    } else {
      clearAllTag();
    }
  }, [fieldBoxData]);
  useEffect(() => {
    console.log('变化', initboxData);
    if (initboxData) {
      setBoxData([...initboxData]);
      onChange && onChange(initboxData);
    }
  }, [initboxData]);
  const handleDelFormula = (idx: number, name: React.ReactNode, idxFormulaItem: number) => {
    // setDelPopVisible(true);
    // setCurrentName(name);
    // setCurrentIdx(idx);
    // setCurrentIdxFormulaItem(idxFormulaItem);
    setConditionFormulaData((prevState) => {
      const copyPreState = cloneDeep(prevState);
      copyPreState[idxFormulaItem].boxData.splice(idx, 1);
      onChange && onChange(copyPreState);
      return copyPreState;
    });
  };

  const moveBox = (dragIndex: number, hoverIndex: number) => {
    setBoxData((prevCards: DragTagItem[]) => {
      onChange &&
        onChange(
          update(prevCards, {
            $splice: [
              [dragIndex, 1],
              [hoverIndex, 0, prevCards[dragIndex] as DragTagItem],
            ],
          }),
        );
      return update(prevCards, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, prevCards[dragIndex] as DragTagItem],
        ],
      });
    });
  };

  const moveBoxFormula = (dragIndex: number, hoverIndex: number, idxFormulaItem: number) => {
    setConditionFormulaData((prevState) => {
      const copyPreState = cloneDeep(prevState);
      copyPreState[idxFormulaItem].boxData = update(copyPreState[idxFormulaItem].boxData, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, copyPreState[idxFormulaItem].boxData[dragIndex] as DragTagItem],
        ],
      });
      onChange && onChange(copyPreState);
      return copyPreState;
    });
  };
  const handleChangeNumber = (value: number | undefined, index: number) => {
    setBoxData((prevState) => {
      const copyBoxData = cloneDeep(prevState);
      copyBoxData[index].value = value;
      onChange && onChange(copyBoxData);
      return copyBoxData;
    });
  };

  const handleChangeNumberFormula = (
    value: number | undefined,
    index: number,
    boxData: DragTagItem[],
    idxFormulaItem: number,
  ) => {
    setConditionFormulaData((prevState) => {
      const copyPreState = cloneDeep(prevState);
      copyPreState[idxFormulaItem].boxData[index].value = value;
      onChange && onChange(copyPreState);
      return copyPreState;
    });
  };

  const handleSetCustField = (index: number, name: string) => {
    setCustFieldVisible(true);
    if (name !== '自定义字段') {
      form.setFieldsValue({
        fieldKey: name,
      });
    }
    setCurrentIdx(index);
  };

  const handleSetCustFieldFormula = (index: number, name: string, idxFormulaItem: number) => {
    setCustFieldVisible(true);
    if (name !== '自定义字段') {
      form.setFieldsValue({
        fieldKey: name,
      });
    }
    setCurrentIdx(index);
    setCurrentIdxFormulaItem(idxFormulaItem);
  };

  const evaluateIsCorrect = useMemo(() => {
    const data = boxData.map((item) => item.computerValue);
    if (data.length === 0) return false;
    const str = data.join('');
    const bool = evaluateExpression(str);
    return bool;
  }, [boxData]);

  const onAddConditionItem = () => {
    setConditionFormulaData((prevState) => {
      if (prevState?.length === maxFormulaCount) {
        return [...prevState];
      }
      return [...prevState, { boxData: [] }];
    });
  };

  const onDeleteFormulaItem = (idx: number) => {
    setConditionFormulaData((prevState) => {
      const copyConditionFormulaData = cloneDeep(prevState);
      copyConditionFormulaData.splice(idx, 1);
      onChange && onChange(copyConditionFormulaData);
      return copyConditionFormulaData;
    });
  };

  const changeJudgeField = (val: string | undefined, idxFormulaItem: number) => {
    setConditionFormulaData((prevState) => {
      const copyPreState = cloneDeep(prevState);
      copyPreState[idxFormulaItem].field = val;
      onChange && onChange(copyPreState);
      return copyPreState;
    });
  };
  const changeJudgeSymbol = (val: '>' | '<' | '>=' | '<=' | '=', idxFormulaItem: number) => {
    setConditionFormulaData((prevState) => {
      const copyPreState = cloneDeep(prevState);
      copyPreState[idxFormulaItem].judgeSymbol = val;
      onChange && onChange(copyPreState);
      return copyPreState;
    });
  };
  const changeJudgeValue = (val: number | undefined, idxFormulaItem: number) => {
    setConditionFormulaData((prevState) => {
      const copyPreState = cloneDeep(prevState);
      copyPreState[idxFormulaItem].judgeValue = val;
      onChange && onChange(copyPreState);
      return copyPreState;
    });
  };

  useImperativeHandle(ref, () => {
    if (isConditionFormula) {
      let copyConditionFormulaData = cloneDeep(conditionFormulaData);
      copyConditionFormulaData = copyConditionFormulaData.map((v) => {
        const copyBoxData = v.boxData;
        const data = copyBoxData.map((item: DragTagItem) => item.computerValue);
        const str = data.join('');
        const bool = evaluateExpression(str);
        return {
          ...v,
          evaluateIsCorrect: data.length === 0 ? false : bool,
        };
      });
      return {
        getComputerData: () => copyConditionFormulaData,
        // copyConditionFormulaData,
        setComputerData: (data: FormulaProps[]) => {
          setConditionFormulaData(data);
        },
        renameKeysInObject,
      };
    }

    return {
      getComputerData: () => boxData,
      // boxData,
      evaluateIsCorrect,
      setComputerData: (data: DragTagItem[]) => {
        setBoxData(data);
      },
      renameKeysInObject,
    };
  });

  const handleClickAddBox = (item: DragTagItem) => {
    const { customized_field, tag_type, index, name, formulaTemplate } = item;
    setBoxData((prev: DragTagItem[]) => {
      let prevCopy = cloneDeep([...prev, item]);
      // 公式模板
      if (Array.isArray(formulaTemplate) && formulaTemplate.length > 0) {
        prevCopy = [...prev, ...formulaTemplate];
      }
      onChange && onChange(prevCopy);
      return prevCopy;
    });
    // 自定字段drop后打开弹窗
    if (customized_field && tag_type === 'field') {
      // 模板的时候打开自定义字段 索引是 boxData.length + item.formulaTemplate.length 减去 两边括弧 2
      // name 是 自定义字段
      if (Array.isArray(formulaTemplate) && formulaTemplate.length > 0) {
        handleSetCustField(boxData.length + formulaTemplate.length - 2, '自定义字段');
      } else {
        handleSetCustField(boxData.length, name);
      }
    }
  };

  return (
    <>
      <LeftItem
        isDisableAll={isDisableAll}
        isConditionFormula={isConditionFormula}
        controlBoxData={controlBoxData}
        fieldBoxData={fieldBoxData}
        symbolBoxData={symbolBoxData}
        handleClickAddBox={handleClickAddBox}
      />
      {isConditionFormula ? (
        <RightItemFormula
          isDisableAll={isDisableAll}
          maxFormulaCount={maxFormulaCount}
          fieldBoxData={fieldBoxData}
          sourceData={conditionFormulaData}
          onAddConditionItem={onAddConditionItem}
          onDeleteFormulaItem={onDeleteFormulaItem}
          handleSetCustField={handleSetCustFieldFormula}
          handleChangeNumber={handleChangeNumberFormula}
          moveBox={moveBoxFormula}
          handleDrop={handleDropFormula}
          handleDel={handleDelFormula}
          clearAllTag={clearAllTagFormula}
          changeJudgeField={changeJudgeField}
          changeJudgeValue={changeJudgeValue}
          changeJudgeSymbol={changeJudgeSymbol}
        />
      ) : (
        <RightItem
          isDisableAll={isDisableAll}
          boxData={boxData}
          handleSetCustField={handleSetCustField}
          handleChangeNumber={handleChangeNumber}
          moveBox={moveBox}
          handleDrop={handleDrop}
          handleDel={handleDel}
          clearAllTag={clearAllTag}
        />
      )}

      <XlbModal
        bordered={false}
        isCancel
        title={
          <div style={{ color: '#86909C' }}>
            删除
            {currentName}
          </div>
        }
        open={delPopVisible}
        centered
        onOk={() => {
          if (isConditionFormula) {
            setConditionFormulaData((prevState) => {
              const copyPreState = cloneDeep(prevState);
              copyPreState[currentIdxFormulaItem].boxData.splice(currentIdx, 1);
              onChange && onChange(copyPreState);
              return copyPreState;
            });
            setDelPopVisible(false);
          } else {
            setBoxData((prevState) => {
              const copyBoxData = cloneDeep(prevState);
              copyBoxData.splice(currentIdx, 1);
              onChange && onChange(copyBoxData);
              return copyBoxData;
            });
            setDelPopVisible(false);
          }
        }}
        onCancel={() => setDelPopVisible(false)}
      >
        <div>删除数据不可恢复，是都继续？</div>
      </XlbModal>
      <XlbModal
        width={430}
        wrapClassName="cust-field-modal"
        isCancel
        title="自定义字段"
        open={custFieldVisible}
        centered
        onOk={() => {
          if (isConditionFormula) {
            form.validateFields().then((values) => {
              const { fieldKey } = values;
              setConditionFormulaData((prevState) => {
                const copyPreState = cloneDeep(prevState);
                copyPreState[currentIdxFormulaItem].boxData[currentIdx].name = fieldKey;
                onChange && onChange(copyPreState);
                return copyPreState;
              });
              setCustFieldVisible(false);
            });
          } else {
            form.validateFields().then((values) => {
              const { fieldKey } = values;
              setBoxData((prevState) => {
                const copyBoxData = cloneDeep(prevState);
                copyBoxData[currentIdx].name = fieldKey;
                onChange && onChange(copyBoxData);
                return copyBoxData;
              });
              setCustFieldVisible(false);
            });
          }
        }}
        onCancel={() => setCustFieldVisible(false)}
      >
        <XlbForm isHideDate form={form} formList={formList} />
      </XlbModal>
    </>
  );
};
export default memo(forwardRef(Container));
