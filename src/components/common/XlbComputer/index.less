.cust-field-modal {
  .ant-modal-body {
    .ant-form {
      margin: 20px 0 40px 0;
      .ant-form-item {
        margin: 0;
      }
    }
  }
}
.public-drag-tag {
  background: #f7f8fa;
  cursor: move;
  font-size: 14px;
  height: 28px;
  padding: 0px 8px;
  margin-right: 8px;
  margin-bottom: 12px;
  display: flex;
  color: #1d2129 !important;
  align-items: center;
  text-align: center;
  border-radius: 4px;
  position: relative;
  &:is(.public-drag-tag-input) {
    padding: 0 0;
    background: unset !important;
  }
  &:hover {
    .clear {
      display: inline-block;
    }
  }
  .clear {
    position: absolute;
    top: -9px;
    right: -9px;
    font-size: 24px;
    cursor: pointer;
    display: none;
    z-index: 1;
  }
}
.xlb-computer * {
  box-sizing: content-box;
}
.xlb-computer {
  .xlb-computer-content {
    position: relative;
    display: flex;
    width: 100%;
    .left-item {
      overflow: auto;
      border-bottom-left-radius: 4px;
      border-top-left-radius: 4px;
      padding: 14px 16px;
      flex: 4;
      // border: 1px solid #e5e6ea;
      border: none;
      position: relative;
      .left-item-mask {
        position: absolute;
        width: 100%;
        height: 100%;
        inset: 0;
        z-index: 999;
        opacity: 0;
      }
      .left-item-field {
        .title {
          font-weight: 400;
          font-size: 14px;
          color: #1d2129;
          margin-bottom: 6px;
          display: flex;
          align-items: center;
        }
        .field-item {
          display: flex;
          width: 100%;
          flex-wrap: wrap;
          .symbol {
            padding: 0 15px !important;
          }
        }
      }
    }
    .right-item {
      border-right: 1px solid #e5e6ea;
      border-bottom: none;
      border-top: none;
      border-radius: 0;
      overflow: auto;
      padding: 14px 16px;
      flex: 5;
      // border: 1px solid #e5e6ea;
      border-left: none;
      position: relative;
      .right-item-mask {
        position: absolute;
        width: 100%;
        height: 100%;
        inset: 0;
        z-index: 999;
        opacity: 0;
      }
      .add-condition {
        font-weight: 500;
        cursor: pointer;
        font-size: 14px;
        color: #3d66fe;
        position: relative;
        .add-condition-mask {
          position: absolute;
          width: 100%;
          height: 100%;
          inset: 0;
          z-index: 999;
          opacity: 0;
        }
        .icon-jia {
          position: absolute;
          left: 0;
          top: 1px;
          font-weight: 400;
          font-size: 13px;
          color: #86909c;
        }
        .add {
          font-weight: 500;
          font-size: 14px;
          color: #3d66fe;
        }
        .tips {
          color: #86909c;
          font-size: 13px !important;
          font-weight: 400 !important;
          vertical-align: text-top;
        }
      }
      .right-item-content {
        .public-drag-tag {
          background: #f2f3f5;
        }
        border: 1px dashed #e5e6ea;
        border-radius: 4px;
        min-height: 172px;
        height: calc(100% - 22px);
        padding: 10px 12px;
        align-content: start;
        display: flex;
        flex-wrap: wrap;
        position: relative;
        .clear-allTag {
          position: absolute;
          right: 12px;
          bottom: 8px;
          font-weight: 400;
          font-size: 14px;
          color: #86909c;
          cursor: pointer;
          display: flex;
          align-items: center;
        }
        .placeholder-tip {
          font-weight: 400;
          font-size: 13px;
          line-height: 28px;
          color: #c9cdd4;
        }
        .right-item-tag {
          &:is(.symbol) {
            padding: 0 15px !important;
          }
          &:is(.first-tag) {
            .public-drag-tag;
            margin-right: 8px;
            line-height: 28px;
            background: none;
            font-weight: bold;
            font-size: 14px;
            color: #1d2129;
            padding-left: 0;
          }
        }
      }
    }
    .detail-item {
      flex: 5;
      padding: 14px 16px;
      height: calc(100% - 28px);

      .detail-panel {
        border: 1px solid #e5e6ea;
        border-radius: 4px;
        height: 100%;
      }
      .detail-item-field {
        padding: 12px 16px;
        height: calc(100% - 55px - 24px);
        overflow-y: auto;
        .title {
          font-weight: 400;
          font-size: 14px;
          color: #1d2129;
          margin-bottom: 6px;
          display: flex;
          align-items: center;
        }
        .field-item {
          display: flex;
          width: 100%;
          flex-wrap: wrap;
          .symbol {
            padding: 0 15px !important;
          }
        }
        .ant-form-item-label {
          width: auto;
        }
        .ant-form-item-required {
          width: 100%;
        }
        .customConditionLabel {
          width: 100%;
          display: flex;
          justify-content: space-between;
          .setConditionBtn {
            color: #3d66fe;
            cursor: pointer;
          }
        }
      }
      .bottomBtns {
        display: flex;
        justify-content: flex-end;
        padding: 12px 16px;
        border-top: 1px solid #e5e6ea;
      }
    }
  }
}
