import useDrag from '@/hooks/useDrag';
import { XlbModal, XlbInput,XlbTreeAntd,XlbIcon} from '@xlb/components';
import { useEffect, useState, useRef } from 'react';
import { XlbFetch } from '@xlb/utils';
import { API_BASE_URL } from '@/constants/common';
import { getareaTree } from '@/utils/kit';
import styles from './index.module.less';
import { debounce } from '../../../pages/roleManage/item/util';
import isEmpty from 'lodash/isEmpty';
const { Search } = XlbInput;
const XlbAreaTypeModal = (props: any) => {
  const { isAreaModalVisible, setIsAreaModalVisible, selectedAreaObj, postUrl, title, selectAreaSubmit } = props;
  const [treeData, setTreeData] = useState<any>([]);
  const checkNodesArray = useRef<[]>([]);
  const originData = useRef<[]>([]);
  const [expandKeys, setExpandKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]); // 选中的节点
  const callerArr = (array: any) => {
    array.forEach((e: any) => {
      if (e.children) {
        e.isLeaf = false;
        e.icon = <XlbIcon style={{color:"#80a5ff",paddingTop:4}} size={16} name="wenjianjia"/>
        callerArr(e.children);
      } else {
        e.isLeaf = true;
        e.icon = <XlbIcon style={{color:"#80a5ff",paddingTop:3}} size={16} name="wenjian"/>
      }
    });
    return array;
  };
  const handleCancel = () => {
    setIsAreaModalVisible(false);
  };

  useEffect(() => {
    if(isEmpty(treeData)){
      return 
    }
    const selectedkeys = selectedAreaObj.map((item: any) => {
      return item.key || item.code;
    });
    setCheckedKeys(selectedkeys);
    setExpandKeys(selectedkeys);
  }, [selectedAreaObj, isAreaModalVisible,treeData]);

  const getGoodsClassList = async () => {
    const res = await XlbFetch.post(`${API_BASE_URL}${postUrl}`, {
      company_id: 1000,
      operator_store_id: 100000000005,
    });

    if (res.code == '0') {
      originData.current = res.data;
      const data: any = getareaTree(res.data);
      setTreeData(callerArr(data));
    }
  };

  const handleConfirm = () => {
    selectAreaSubmit(checkNodesArray.current);
    setIsAreaModalVisible(false);
  };

  const onCheck = (checked, info) => {
    checkNodesArray.current = info.checkedNodes;
    setCheckedKeys(checked.checked);
  };
  const onChange = debounce((e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    const newExpandedKeys = originData.current
      .map((item) => {
        if (item.name.indexOf(value) > -1) {
          return item?.parent_code;
        }
        return null;
      })
      .filter((item, i, self): item is React.Key => !!(item && self.indexOf(item) === i));
    setExpandKeys(newExpandedKeys);
    setAutoExpandParent(true);
  }, 500);

  const handleExpand = (val) => {
    setExpandKeys(val);
    setAutoExpandParent(false);
  };

  useEffect(() => {
    if (isAreaModalVisible) {
      getGoodsClassList();
    }
  }, [isAreaModalVisible]);

  useDrag('.ant-modal-header', '.ant-modal-content');

  return (
    <XlbModal
      title={title || '选择区域'}
      width={360}
      open={isAreaModalVisible}
      wrapClassName="xlbDialog"
      onCancel={() => handleCancel()}
      onOk={() => handleConfirm()}
      bodyStyle={{ height: 550, padding: 0 }}
      destroyOnClose={true}
      centered
      maskClosable={false}
    >
      <div className={styles.modalContainer}>
        <Search  placeholder='请输入关键字' style={{  padding: "15px 15px 10px 15px" }} onChange={onChange} />
        <XlbTreeAntd
          expandedKeys={expandKeys}
          autoExpandParent={autoExpandParent}
          onExpand={handleExpand}
          showIcon
          checkable
     
          fieldNames={{
            title: 'name',
            key: 'code',
            children: 'children',
          }}
          checkStrictly
          onCheck={onCheck}
          checkedKeys={checkedKeys}
          treeData={treeData}
        />
      </div>
    </XlbModal>
  );
};

export default XlbAreaTypeModal;
