import { InfoCircleOutlined } from '@ant-design/icons';
import {
  STATE_TYPE,
  AUDIT_TYPE,
  AUDIT_RESULT,
  FREQUENCY_TYPE,
  ITEM_TYPE,
  MONTHS_OPTION,
  EXECUTIVE_STANDARD_LIST,
  executiveStandardType,
} from './data';
import { DataType } from '@xlb/components/dist/components/XlbTree/type';
import { CreateMapItem } from '@xlb/components/dist/lowcodes/XlbProForm/type';
import { message, Tooltip } from 'antd';
import { XlbSelect, XlbIcon } from '@xlb/components';
import { ProFormDependency, ProForm } from '@ant-design/pro-components';
import copy from 'copy-to-clipboard';
import { projectType } from '@/pages/auditCriteria/data';
import dayjs from 'dayjs';
import { operationType } from '@/pages/systemLog/data';

export const CenterFieldKeyMap = {
  centerInput: 'input',
  centerSelect: 'select',
  centerUpload: 'upload',
  centerTextArea: 'textarea',
  centerCode: 'categroyCode',
  centerName: 'categroyName',
  centerMemo: 'memo',
  centerCatory: 'parentId',
  centerItemBrand: 'itemBrandId',
  centerQuarityCatory: 'centerQuarityCatoryParentId',
  centerQualityReportProblem: 'centerQualityReportProblem',
  centerQualityReportDelivery: 'centerQualityReportDelivery',
  centerQualityReportProducers: 'centerQualityReportProducers',
  centerQualityReportDeliveryCenter: 'centerQualityReportDeliveryCenter',
  centerInputNumber: 'centerInputNumber',
  centerProjectType: 'projectType',
  centerProjectTypeDetail: 'centerProjectTypeDetail',
  levelRules: 'levelRules',
  scoreRatio: 'scoreRatio',
  centerStoreId: 'centerStoreId',
  controlFrequency: 'controlFrequency',
  userIds: 'userIds',
  goodsCategory: 'goodsCategory',
  categoryManage: 'categoryManage',
  exceptionCategory_Parent: 'exceptionCategory_Parent',
  centerQualityReportProducerszltb: 'centerQualityReportProducerszltb',
  fromList: 'fromList',
  specificationByJin: 'specificationByJin',
  expireType: 'expireType',
  packageBar: 'packageBar',
  executiveStandard: 'executiveStandard',
  executiveStandardType: 'executiveStandardType',
  centimeter: 'centimeter',
  centerUploadPicture: 'centerUploadPicture',
  centerItemByBoolean: 'centerItemByBoolean',
  productDepartment: 'productDepartment',
  productBrand: 'productBrand',
  centerUnit: 'centerUnit',
  priceWithRadio: 'priceWithRadio',
  centerPrice: 'centerPrice',
  productAttributes: 'productAttributes',
  deliveryPriceWithRadio: 'deliveryPriceWithRadio',
  wholesalePriceWithRadio: 'wholesalePriceWithRadio',
  standardPriceWithRadio: 'standardPriceWithRadio',
  taxRate: 'taxRate',
  centerOrgId: 'centerOrgId',
  centerCompanyAsOrgId: 'centerCompanyAsOrgId',
  commonScore: 'commonScore',
  categoryScorePercentage: 'categoryScorePercentage',
  supplierAuditUndercut: 'supplierAuditUndercut',
  centerexceptionType: 'centerexceptionType',
  // orgId: 'org_id',
  centerCompanyOrgId: 'centerCompanyOrgId',
  commonTemplate: 'commonTemplate',
  specialTemplate: 'specialTemplate',
  auditProjectType: 'auditProjectType',
  orgId: 'orgId',
  specUnit: 'specUnit',
  deliveryPriceCompute: 'deliveryPriceCompute',
  wholesaleCompute: 'wholesaleCompute',
  goodsCategoryId: 'goodsCategoryId',
  centerCategoryId: 'centerCategoryId',
  purchaseVolume: 'purchaseVolume',
  deliveryVolume: 'deliveryVolume',
  externalReportList: 'externalReportList',
  disabledExternalReportList: 'disabledExternalReportList',
  operationTypeList: 'operationTypeList',
  externalReportListNoRules: 'externalReportListNoRules',
  foodsumdays: 'foodsumdays',
  // BUSINESS: 'business',
  // ADMINISTRATIVE: 'administrative',
  firstSecondLevelOrgIds: 'firstSecondLevelOrgIds',
  formSpanItemLarge: 'formSpanItemLarge',
};

const transfromData = (data: any) => {
  return data?.map((item: any) => {
    if (item.children.length > 0) {
      return {
        title: item.name,
        value: item.id,
        children: transfromData(item.children),
      };
    } else {
      return {
        title: item.name,
        value: item.id,
      };
    }
  });
};

export const centerFormList: Array<CreateMapItem> = [
  {
    componentType: 'input',
    tag: 'center',
    id: CenterFieldKeyMap.centerInput,
    dependencies: ['quality_type'],
  },
  {
    componentType: 'select',
    tag: 'center',
    id: CenterFieldKeyMap.centerSelect,
    dependencies: ['quality_type'],
  },
  {
    componentType: 'upload',
    tag: 'center',
    id: CenterFieldKeyMap.centerUpload,
    dependencies: ['quality_type'],
  },
  {
    componentType: 'textArea',
    tag: 'center',
    id: CenterFieldKeyMap.centerTextArea,
    dependencies: ['quality_type'],
  },
  {
    componentType: 'inputDialog',
    tag: 'center',
    id: CenterFieldKeyMap.centerQualityReportDeliveryCenter,
    fieldProps: {
      dialogParams: {
        type: 'centerStore',
        dataType: 'tree',
        isMultiple: false,
        url: '/center/hxl.center.store.delivery.findall',
      },
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!value?.length) return;
        return value[0];
      },
    },
  },
  {
    componentType: 'select',
    tag: 'center',
    id: CenterFieldKeyMap.centerItemBrand,
    async request(formValues, anybaseURL, globalFetch) {
      const res = await globalFetch.post(anybaseURL + '/center/hxl.center.itembrand.find', { page_number: 0 });
      if (res.code == 0) {
        return res.data.map((item: any) => {
          return {
            ...item,
            label: item.name,
            value: item.id,
          };
        });
      }
      return [];
    },
  },
  {
    componentType: 'inputDialog',
    tag: 'center',
    id: CenterFieldKeyMap.centerQualityReportProducers,
    fieldProps: {
      dialogParams: {
        type: 'supplier',
        dataType: 'tree',
        isMultiple: false,

        data: {
          supplier_type: 'PRODUCER',
        },
      },
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        console.log('value', value);
        if (!value?.length) return;
        return value[0];
      },
    },
  },
  {
    tag: 'center',
    id: CenterFieldKeyMap.centerQualityReportProblem,
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      const res = await globalFetch.post(anybaseURL + '/center/hxl.center.problemcategor.find', { parent_id: 0 });
      if (res.code == 0) {
        return res.data.map((item: any) => {
          return {
            ...item,
            label: item.name,
            value: item.id,
          };
        });
      }
      return [];
    },
    componentType: 'select',
  },
  {
    componentType: 'select',
    tag: 'center',
    id: CenterFieldKeyMap.centerQualityReportDelivery,
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(anybaseURL + '/center/hxl.center.foodcategory.find', { level: 2 });
      if (res.code == 0) {
        return res.data.map((item: any) => ({
          label: item.name,
          value: item.id,
        }));
      }
      return [];
    },
  },
  {
    tag: 'center',
    label: '组织名称',
    id: 'organizationNameName',
    name: 'name',
    componentType: 'input',
  },
  {
    tag: 'center',
    label: '类别',
    id: 'businessscopecategory',
    name: 'category_ids',
    fieldProps: {
      treeModalConfig: {
        requestParams: {
          business_type: '0',
        },
        params: {
          business_type: '0',
        },
        // @ts-ignore
        title: '选择经营范围分类', // 标题
        url: '/center/hxl.center.businessscopecategory.find',
        multiple: true,
        checkable: true,
        dataType: DataType.TREE,
        type: 'base',
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'center',
    label: '过滤/门店零售价',
    id: 'checked',
    name: 'check',
    fieldProps: {
      options: [
        { value: 'flag', label: '显示价格异常商品' },
        // { value: "read_item_attr", label: "读取总部停售属性" },
        { value: 'query_diff', label: '显示修改价' },
      ],
    },
    formItemProps: {
      label: '  ',
    },
    group: false,
    componentType: 'checkbox',
  },
  {
    tag: 'center',
    id: 'rateCompare',
    componentType: 'group',
    fieldProps: {
      formList: [
        {
          tag: 'center',
          label: '利率比较',
          id: 'price',
          name: 'priceValue',
          componentType: 'input',
          fieldProps: {
            readOnly: true,
          },
        },
        {
          tag: 'center',
          label: '',
          id: 'priceSymbol',
          name: 'symbol',
          componentType: 'select',
          fieldProps: {
            options: [
              { label: '≥', value: '>=' },
              { label: '≤', value: '<=' },
            ],
          },
        },
        {
          tag: 'center',
          label: '',
          id: 'priceCompare',
          name: 'compare',
          componentType: 'inputNumber',
          textAfter: '%',
          fieldProps: {
            max: 100,
            min: 0,
            precision: 2,
            controls: false,
          },
        },
      ],
    },
  },
  /**带权限过滤的 */
  {
    tag: 'center',
    label: '公司',
    id: 'companyAuthInit',
    name: 'company_id',
    componentType: 'select',
    request: async (params, baseURL, globalFetch) => {
      const {} = params;
      const result = await globalFetch.post(`${baseURL}/center/hxl.center.company.auth.find`, {});
      if (result?.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        }
      }
    },
    handleDefaultValue(data) {
      if (data) {
        return data![0]?.value;
      }
    },
  },
  {
    tag: 'center',
    label: '公司',
    id: 'company',
    name: 'company_ids',
    componentType: 'select',
    fieldProps: { mode: 'multiple' },
    request: async (params, baseURL, globalFetch) => {
      const {} = params;
      const result = await globalFetch.post(`${baseURL}/center/hxl.center.company.findAll`, {});
      if (result?.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        }
      }
    },
  },
  {
    tag: 'center',
    label: '公司',
    id: 'companySingle',
    name: 'company_id',
    componentType: 'select',
    request: async (params, baseURL, globalFetch) => {
      const {} = params;
      const result = await globalFetch.post(`${baseURL}/center/hxl.center.company.findAll`, {});
      if (result?.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        }
      }
    },
  },
  {
    tag: 'center',
    label: '类别编号',
    id: CenterFieldKeyMap.centerCode,
    name: 'code',
    componentType: 'input',
  },
  {
    tag: 'center',
    label: '类别名称',
    id: CenterFieldKeyMap.centerName,
    name: 'name',
    componentType: 'input',
  },
  {
    tag: 'center',
    label: '食品分类上级类别',
    id: CenterFieldKeyMap.centerCatory,
    name: 'parent_id',
    componentType: 'select',
    request: async (params, baseURL, globalFetch) => {
      const result = await globalFetch.post(`${baseURL}/center/hxl.center.foodcategory.find`, { parent_id: 0 });
      if (result.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        }
      }
    },
  },
  {
    tag: 'center',
    label: '备注',
    id: CenterFieldKeyMap.centerMemo,
    name: 'memo',
    componentType: 'textArea',
  },
  {
    tag: 'center',
    label: '质量问题上级类别',
    id: CenterFieldKeyMap.centerQuarityCatory,
    name: 'parent_id',
    componentType: 'treeSelect',
    itemSpan: 12,
    fieldProps: {
      treeIcon: true,
      showSearch: true,
      treeNodeLabelProp: 'title',
    },
    async request(params: any, baseUrl, globalFetch) {
      // const { type } = params || {};
      const result = await globalFetch.post(`${baseUrl}/center/hxl.center.problemcategor.find`, { level: 2 });
      if (result.code == 0) {
        if (Array.isArray(result.data)) {
          return transfromData(result.data);
        }
      }
    },
  },
  {
    tag: 'center',
    label: '审核类型',
    id: 'auditType',
    name: 'type',
    componentType: 'select',
    fieldProps: {
      options: AUDIT_TYPE,
    },
  },
  {
    tag: 'center',
    label: '单据状态',
    id: 'orderAuditStatus',
    name: 'state',
    componentType: 'select',
    fieldProps: {
      options: STATE_TYPE,
    },
  },
  {
    tag: 'center',
    label: '审核结果',
    id: 'auditResult',
    name: 'audit_result',
    componentType: 'select',
    fieldProps: {
      options: AUDIT_RESULT,
    },
  },
  {
    tag: 'center',
    label: '采购经理',
    componentType: 'inputDialog',
    id: 'purchaseManager',
    fieldProps: {
      dialogParams: {
        isLeftColumn: false,
        type: 'purchaseManager',
        dataType: 'tree',
        isMultiple: false,
        data: {
          if_purchase_manager: true,
        },
      },
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!value?.length) return;
        return value[0];
      },
    },
  },
  {
    tag: 'center',
    label: '审核模板',
    id: 'auditTemplate',
    name: 'template_id',
    componentType: 'select',
    request: async (params, baseURL, globalFetch) => {
      const {} = params;
      const result = await globalFetch.post(`${baseURL}/center/hxl.center.supplieraudittemplate.find`, {});
      if (result?.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.template_name,
              value: item.id,
            };
          });
        }
      }
    },
  },
  /**食安/供应商审核 */
  {
    tag: 'center',
    label: '通用模板',
    id: CenterFieldKeyMap.commonTemplate,
    name: 'template_id',
    componentType: 'select',
    request: async (params, baseURL, globalFetch) => {
      const {} = params;
      const result = await globalFetch.post(`${baseURL}/center/hxl.center.supplieraudittemplate.find`, {
        audit_type: 'COMMON_AUDIT',
      });
      if (result?.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.template_name,
              value: item.id,
            };
          });
        }
      }
    },
  },
  {
    tag: 'center',
    label: '专用模板',
    id: CenterFieldKeyMap.specialTemplate,
    name: 'template_id',
    componentType: 'select',
    request: async (params, baseURL, globalFetch) => {
      const {} = params;
      const result = await globalFetch.post(`${baseURL}/center/hxl.center.supplieraudittemplate.find`, {
        audit_type: 'SPECIAL_AUDIT',
      });
      if (result?.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.template_name,
              value: item.id,
            };
          });
        }
      }
    },
  },
  /**食安/审核标准 */
  {
    tag: 'center',
    label: '分值',
    id: CenterFieldKeyMap.centerInputNumber,
    name: 'score',
    componentType: 'inputNumber',
  },
  /**食安/经营项目管理 */
  {
    tag: 'center',
    label: '项目类型',
    id: CenterFieldKeyMap.centerProjectType,
    name: 'project_type_id',
    componentType: 'select',
    // fieldProps: {
    //   mode: 'multiple'
    // },
    request: async (params, baseURL, globalFetch) => {
      const result = await globalFetch.post(`${baseURL}/center/hxl.center.storeproject.find`, {});
      if (result.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.type,
              value: item.id,
            };
          });
        }
      }
    },
  },
  {
    tag: 'center',
    label: '项目类型',
    id: CenterFieldKeyMap.centerProjectTypeDetail,
    name: 'project_type_id',
    componentType: 'select',
    dependencies: ['card_type'],
    request: async (params, baseURL, globalFetch) => {
      if (!params.card_type) return [];
      const result = await globalFetch.post(`${baseURL}/center/hxl.center.storeproject.find`, {
        card_type: params.card_type,
      });
      if (result.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.type,
              value: item.id,
            };
          });
        }
      }
    },
  },
  /**食安参数 */
  {
    tag: 'center',
    id: CenterFieldKeyMap.levelRules,
    componentType: 'group',
    label: '供应商审核等级规则(百分制',
    formItemProps: {
      tooltip: '优秀 > 良好 > 及格',
      colon: false,
    },
    itemSpan: 24,
    fieldProps: {
      formList: [
        {
          tag: 'center',
          label: '优秀',
          id: 'excellentScore',
          name: 'excellent_score',
          componentType: 'inputNumber',
          fieldProps: {
            max: 100,
            min: 1,
            precision: 0,
            controls: false,
          },
          itemSpan: 24,
          formItemProps: { colon: false, wrapperCol: { style: { width: 80 } } },
        },
        {
          tag: 'center',
          label: '良好',
          id: 'goodScore',
          name: 'good_score',
          componentType: 'inputNumber',
          fieldProps: {
            max: 100,
            min: 1,
            precision: 0,
            controls: false,
          },
          itemSpan: 24,
          formItemProps: { colon: false, wrapperCol: { style: { width: 80 } } },
        },
        {
          tag: 'center',
          label: '及格',
          id: 'passScore',
          name: 'pass_score',
          componentType: 'inputNumber',
          fieldProps: {
            max: 100,
            min: 1,
            precision: 0,
            controls: false,
          },
          itemSpan: 24,
          formItemProps: { colon: false, wrapperCol: { style: { width: 80 } } },
        },
      ],
    },
  },
  {
    tag: 'center',
    label: '所属门店',
    id: CenterFieldKeyMap.centerStoreId,
    fieldProps: {
      dialogParams: {
        type: 'centerStore',
        dataType: 'tree',
        isMultiple: false,
      },
    },
    componentType: 'inputDialog',
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!value?.length) return;
        return value[0];
      },
    },
  },
  {
    tag: 'center',
    id: CenterFieldKeyMap.scoreRatio,
    componentType: 'group',
    label: '供应商审核各标准得分占比(%)',
    itemSpan: 24,
    formItemProps: {
      tooltip: '合格 > 轻微 > 严重',
      colon: false,
    },
    fieldProps: {
      formList: [
        {
          tag: 'center',
          label: '合格',
          id: 'qualifiedScore',
          name: 'qualified_score',
          componentType: 'inputNumber',
          fieldProps: {
            max: 100,
            min: 1,
            precision: 0,
            controls: false,
          },
          itemSpan: 24,
          formItemProps: { colon: false, wrapperCol: { style: { width: 80 } } },
        },
        {
          tag: 'center',
          label: '轻微',
          id: 'slightScore',
          name: 'slight_score',
          componentType: 'inputNumber',
          fieldProps: {
            max: 100,
            min: 1,
            precision: 0,
            controls: false,
          },
          itemSpan: 24,
          formItemProps: { colon: false, wrapperCol: { style: { width: 80 } } },
        },
        {
          tag: 'center',
          label: '严重',
          id: 'seriousScore',
          name: 'serious_score',
          componentType: 'inputNumber',
          fieldProps: {
            max: 100,
            min: 0,
            precision: 0,
            controls: false,
          },
          itemSpan: 24,
          formItemProps: { colon: false, wrapperCol: { style: { width: 80 } } },
        },
      ],
    },
  },
  {
    tag: 'center',
    componentType: 'select',
    label: '管控频次',
    id: CenterFieldKeyMap.controlFrequency,
    name: 'control_frequency',
    fieldProps: {
      options: FREQUENCY_TYPE,
    },
  },
  {
    tag: 'center',
    label: '用户',
    componentType: 'inputDialog',
    id: CenterFieldKeyMap.userIds,
    fieldProps: {
      dialogParams: {
        isLeftColumn: false,
        type: 'User',
        dataType: 'tree',
        isMultiple: true,
      },
    },
  },
  {
    tag: 'center',
    label: '所属公司',
    id: 'companyDynamicList',
    name: 'detail_list',
    componentType: 'list',
    fieldProps: {
      createText: '添加',
      formList: [
        {
          tag: 'center',
          label: '',
          id: 'scmOrgId',
          name: 'org_id_name',
          componentType: 'select',
          rules: [{ required: true, message: '请选择公司' }],
          itemSpan: 12,
          async request(params: any, baseUrl, globalFetch) {
            const {} = params;
            const result = await globalFetch.post(`${baseUrl}/center/hxl.center.org.find`, { levels: [1, 2] });
            if (result?.code === 0) {
              if (Array.isArray(result.data)) {
                return result.data.map((item: any) => {
                  return {
                    label: item.name,
                    value: item.id,
                  };
                });
              }
            }
          },
          formItemProps: {
            getValueFromEvent(a, b, c) {
              return {
                org_id: b.value,
                org_name: b.label,
              };
            },
            getValueProps(value) {
              return { value: value?.org_id };
            },
          },
        },
        {
          tag: 'center',
          label: '品类经理',
          id: 'centerUserId',
          name: 'user_ids',
          componentType: 'inputDialog',
          itemSpan: 12,
          dependencies: ['org_id'],
          rules: [{ required: true, message: '请选择品类经理' }],
          fieldProps: {
            dialogParams: {
              isLeftColumn: false,
              type: 'purchaseManager',
              dataType: 'tree',
              isMultiple: true,
              data: {
                if_purchase_manager: true,
              },
            },
          },
        },
      ],
    },
  },
  {
    componentType: 'inputDialog',
    tag: 'center',
    id: CenterFieldKeyMap.centerQualityReportProducerszltb,
    fieldProps: {
      dialogParams: {
        type: 'supplier',
        dataType: 'tree',
        isMultiple: true,
        data: {
          supplier_type: 'PRODUCER',
        },
      },
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!value?.length) return;
        return value;
      },
    },
  },
  {
    componentType: 'select',
    tag: 'FSMS',
    id: CenterFieldKeyMap.exceptionCategory_Parent,
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      const res = await globalFetch.post(anybaseURL + '/center/hxl.center.abnormalcategory.findbyparentid', {
        parent_id: 0,
      });
      if (res?.code === 0) {
        return res.data.map((item: any) => ({
          ...item,
          label: item.name,
          value: item.id,
        }));
      }
      return [];
    },
  },
  {
    tag: 'center',
    id: CenterFieldKeyMap.commonScore,
    componentType: 'inputNumber',
    label: '供应商审核通用标准得分占比(%)',
    name: 'common_score_percentage',
    fieldProps: {
      min: 1,
      max: 100,
      precision: 0,
      placeholder: '',
    },
    formItemProps: {
      colon: false,
      labelAlign: 'left',
      labelCol: { style: { width: 223 } },
    },
  },
  {
    tag: 'center',
    id: CenterFieldKeyMap.categoryScorePercentage,
    componentType: 'group',
    label: '供应商审核各类别得分系数',
    itemSpan: 24,
    formItemProps: {
      colon: false,
    },
    fieldProps: {
      formList: [
        {
          tag: 'center',
          label: '食品安全管理要求',
          id: 'foodSafeScore',
          name: 'food_safe_score',
          componentType: 'inputNumber',
          fieldProps: {
            max: 100,
            min: 1,
            precision: 0,
            controls: false,
          },
          itemSpan: 24,
          formItemProps: {
            colon: false,
            wrapperCol: { style: { width: 80 } },
          },
        },
        {
          tag: 'center',
          label: 'GPM要求',
          id: 'gmpScore',
          name: 'gmp_score',
          componentType: 'inputNumber',
          fieldProps: {
            max: 100,
            min: 1,
            precision: 0,
            controls: false,
          },
          itemSpan: 24,
          formItemProps: {
            colon: false,
            wrapperCol: { style: { width: 80 } },
          },
        },
        {
          tag: 'center',
          label: '危害分析与关键控制点',
          id: 'hazardControlScore',
          name: 'hazard_control_score',
          componentType: 'inputNumber',
          fieldProps: {
            max: 100,
            min: 1,
            precision: 0,
            controls: false,
          },
          itemSpan: 24,
          formItemProps: {
            colon: false,
            wrapperCol: { style: { width: 80 } },
          },
        },
        {
          tag: 'center',
          label: '产品类别',
          id: 'productScore',
          name: 'product_score',
          componentType: 'inputNumber',
          fieldProps: {
            max: 100,
            min: 1,
            precision: 0,
            controls: false,
          },
          itemSpan: 24,
          formItemProps: {
            colon: false,
            wrapperCol: { style: { width: 80 } },
          },
        },
      ],
    },
  },
  {
    tag: 'center',
    id: CenterFieldKeyMap.supplierAuditUndercut,
    componentType: 'group',
    itemSpan: 24,
    fieldProps: {
      formList: [
        {
          tag: 'center',
          label: '供应商审核倒扣项目',
          id: 'undercutCategories',
          name: 'undercut_categories',
          componentType: 'select',
          fieldProps: {
            mode: 'multiple',
          },
          request: async (params, baseURL, globalFetch) => {
            const result = await globalFetch.post(`${baseURL}/center/hxl.center.sysdict.find`, {
              dict_type: 'supplier_audit_project',
            });
            if (result.code === 0) {
              if (Array.isArray(result.data)) {
                return result.data.map((item: any) => {
                  return {
                    label: item.dict_label,
                    value: item.dict_value,
                  };
                });
              }
            }
          },
          itemSpan: 24,
          formItemProps: {
            colon: false,
            wrapperCol: { style: { width: 80 } },
          },
        },
        {
          tag: 'center',
          label: '倒扣项目符合率(%)',
          id: 'undercutRate',
          name: 'undercut_rate',
          componentType: 'inputNumber',
          fieldProps: {
            max: 100,
            min: 1,
            precision: 0,
            controls: false,
          },
          itemSpan: 24,
          formItemProps: {
            colon: false,
            wrapperCol: { style: { width: 80 } },
          },
        },
        {
          tag: 'center',
          label: '倒扣分数',
          id: 'undercutScore',
          name: 'undercut_score',
          componentType: 'inputNumber',
          fieldProps: {
            max: 100,
            min: 1,
            precision: 0,
            controls: false,
          },
          itemSpan: 24,
          formItemProps: {
            colon: false,
            wrapperCol: { style: { width: 80 } },
          },
        },
      ],
    },
  },
  {
    tag: 'center',
    label: '异常类型',
    id: CenterFieldKeyMap.centerexceptionType,
    name: 'abnormal_category_parent_id',
    // name: 'abnormal_one_level_id',
    componentType: 'select',
    request: async (params, baseURL, globalFetch) => {
      const result = await globalFetch.post(`${baseURL}/center/hxl.center.abnormalcategory.find`, { level: 1 });
      if (result.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        }
      }
    },
    hidden(formValues) {
      return formValues?.query_models ? !formValues?.query_models.includes('ABNORMAL') : true;
    },
    dependencies: ['query_models'],
  },
  {
    tag: 'center',
    label: '应用组织',
    id: CenterFieldKeyMap?.centerCompanyOrgId,
    name: 'org_parent_ids',
    fieldProps: {
      mode: 'multiple',
    },
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      const res = await globalFetch.post(anybaseURL + '/center/hxl.center.org.find', {
        levels: [2],
      });
      if (res?.code === 0) {
        return res.data.map((item: any) => ({
          ...item,
          label: item.name,
          value: item.id,
        }));
      }
      return [];
    },
    componentType: 'select',
  },
  {
    tag: 'center',
    label: '所属区域',
    id: 'addressId',
    name: 'address_id',
    componentType: 'address',
    fieldProps: {
      url: '/center/hxl.center.area.allcity.find',
      requestParams: { levels: [1, 2] },
      optionsMap: {
        labelKey: 'name',
        id: 'code',
        valueKey: 'code',
        parentId: 'parent_code',
        rootId: '100000',
      },
    },
  },
  /**食安/审核标准 */
  {
    tag: 'center',
    label: '项目',
    id: CenterFieldKeyMap.auditProjectType,
    name: 'category',
    componentType: 'select',
    request: async (params, baseURL, globalFetch) => {
      const result = await globalFetch.post(`${baseURL}/center/hxl.center.sysdict.find`, {
        dict_type: 'supplier_audit_project',
      });
      if (result.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.dict_label,
              value: item.dict_value,
            };
          });
        }
      }
    },
  },
  {
    tag: 'center',
    label: '组织',
    name: 'org_ids',
    id: CenterFieldKeyMap.orgId,
    fieldProps: {
      treeModalConfig: {
        // @ts-ignore
        title: '选择组织',
        url: '/bi/hxl.center.storesale.tree',
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'id',
      },
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!Array.isArray(value)) return;
        return value;
      },
    },
    componentType: 'inputDialog',
  },
  // 入职日期
  {
    tag: 'center',
    label: '入职日期',
    id: 'entryDate',
    name: 'entry_date',
    componentType: 'datePicker',
    fieldProps: {
      placeholder: '',
    },
  },
  {
    componentType: 'list',
    tag: 'CENTER',
    id: CenterFieldKeyMap.fromList,
    label: '选择',
    // itemSpan: 20,
    fieldProps: {
      max: undefined,
      formList: [
        {
          componentType: 'input',
          id: 'listinput',
          tag: 'CENTER',
          name: 'name',
          itemSpan: 20,
          // label: '选项',
          // itemSpan: 22,
        },
      ],
      createText: '新增选项',
    },
  },
  {
    tag: 'center',
    label: '商品品类',
    id: CenterFieldKeyMap.goodsCategoryId,
    name: 'item_public_category_id',
    componentType: 'select',
    request: async (params, baseURL, globalFetch) => {
      const result = await globalFetch.post(`${baseURL}/center/hxl.center.itempubliccategory.list`, {});
      if (result?.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        }
      }
      return [];
    },
  },
  {
    tag: 'center',
    label: '保质期限',
    id: CenterFieldKeyMap.expireType,
    name: 'expire_type_num',
    componentType: 'inputNumber',
    dependencies: ['expire_type'],
    fieldProps: {
      precision: 2,
      defaultValue: 0,
      min: 0,
      addonAfter: (
        <ProForm.Item noStyle name={'expire_type'} rules={[{ required: true, message: '请选择单位' }]}>
          <XlbSelect
            width={80}
            defaultValue={1}
            options={[
              { label: '天', value: 1 },
              { label: '月', value: 2 },
            ]}
          />
        </ProForm.Item>
      ),
      style: { width: '100%' },
    },
  },
  {
    tag: 'center',
    label: '外箱码',
    id: CenterFieldKeyMap.packageBar,
    name: 'package_bar_code',
    dependencies: ['enable_package_bar_code', 'retail_method'],
    componentType: 'input',
    disabled: (obj) => {
      return obj.retail_method === 0;
    },
    fieldProps: {
      addonBefore: (
        <ProFormDependency name={['retail_method']}>
          {(obj) => {
            return (
              <ProForm.Item noStyle name={'enable_package_bar_code'}>
                <XlbSelect
                  disabled={obj?.retail_method === 0}
                  width={80}
                  defaultValue={true}
                  options={[
                    { label: '有', value: true },
                    { label: '无', value: false },
                  ]}
                />
              </ProForm.Item>
            );
          }}
        </ProFormDependency>
      ),
      style: { width: '100%' },
    },
  },
  {
    tag: 'center',
    label: '是否执行企业标准',
    id: CenterFieldKeyMap.executiveStandardType,
    name: 'executive_standard_type',
    dependencies: ['executive_standard'],
    componentType: 'select',
    fieldProps: {
      options: EXECUTIVE_STANDARD_LIST,
      defaultValue: executiveStandardType.NATIONAL_STANDARDS,
    },
  },
  {
    tag: 'center',
    label: '产品执行标准',
    id: CenterFieldKeyMap.executiveStandard,
    name: 'executive_standard',
    dependencies: ['executive_standard_type'],
    componentType: 'input',
  },
  {
    tag: 'center',
    componentType: 'inputNumber',
    id: CenterFieldKeyMap.centimeter,
    fieldProps: {
      precision: 2,
      suffix: 'cm',
      min: 0,
    },
  },
  {
    tag: 'center',
    label: '税率',
    id: CenterFieldKeyMap.taxRate,
    name: 'goodsCategory_id',
    componentType: 'select',
    request: async (params, baseURL, globalFetch) => {
      const result = await globalFetch.post(`${baseURL}/center/hxl.center.baseparam.read`, {});
      if (result?.code === 0) {
        if (Array.isArray(result.data?.tax_rates)) {
          return result.data?.tax_rates?.map((item: any) => {
            return {
              label: item?.toString(),
              value: item,
            };
          });
        }
      }
      return [];
    },
  },
  {
    tag: 'center',
    componentType: 'upload',
    id: CenterFieldKeyMap.centerUploadPicture,
    fieldProps: {
      action: '/center/hxl.center.file.upload',
      maxCount: 1,
      columnNum: 1,
      showUpload: true,
      listType: 'picture-card',
      accept: 'image',
      data: {
        refType: 'SUPPLIER_AUDIT',
        refId: 'sfsffefefe',
      },
    },
  },
  {
    tag: 'center',
    label: '是否',
    id: CenterFieldKeyMap.centerItemByBoolean,
    componentType: 'select',
    fieldProps: {
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
  },
  {
    tag: 'center',
    label: '商品部门',
    id: CenterFieldKeyMap.productDepartment,
    name: 'goodsCategory_id',
    componentType: 'select',
    request: async (params, baseURL, globalFetch) => {
      const result = await globalFetch.post(`${baseURL}/center/hxl.center.dept.find`, {});
      if (result?.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        }
      }
      return [];
    },
  },
  {
    tag: 'center',
    label: '商品品牌',
    id: CenterFieldKeyMap.productBrand,
    name: 'goodsCategory_id',
    componentType: 'select',
    request: async (params, baseURL, globalFetch) => {
      const result = await globalFetch.post(`${baseURL}/center/hxl.center.itembrand.find`, {});
      if (result?.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.name,
              value: item.id,
              center_id: item.center_id,
            };
          });
        }
      }
      return [];
    },
  },
  {
    tag: 'center',
    label: '基本单位',
    id: CenterFieldKeyMap.centerUnit,
    name: 'goodsCategory_id',
    componentType: 'select',
    request: async (params, baseURL, globalFetch) => {
      const result = await globalFetch.post(`${baseURL}/center/hxl.center.itemunit.find`, {});
      if (result?.code === 0) {
        if (Array.isArray(result.data)) {
          return result.data.map((item: any) => {
            return {
              label: item.name,
              value: item.name,
            };
          });
        }
      }
      return [];
    },
  },
  {
    tag: 'center',
    label: '配送价类型',
    id: CenterFieldKeyMap.deliveryPriceWithRadio,
    componentType: 'inputNumber',
    name: 'delivery_type_item',
    dependencies: ['delivery_type'],
    fieldProps: {
      placeholder: '请输入',
      style: { width: '100%' },
      defaultValue: 0,
      min: 0,
      addonBefore: (
        <ProForm.Item noStyle name={'delivery_type'}>
          <XlbSelect width={80} options={ITEM_TYPE} defaultValue={2} />
        </ProForm.Item>
      ),
    },
  },
  {
    tag: 'SCM',
    label: '配送价',
    id: CenterFieldKeyMap.deliveryPriceCompute,
    name: 'deliveryPrice',
    disabled: true,
    componentType: 'inputNumber',
    dependencies: ['state', 'purchase_price', 'delivery_type', 'delivery_type_item'],
    request: (obj) => {
      return obj;
    },
    handleDefaultValue(obj) {
      if (obj.delivery_type === 0) {
        console.log(obj.delivery_type_item, obj.purchase_price);
        return ((obj.delivery_type_item * 0.01 + 1) * Number(obj.purchase_price || 0))?.toFixed(8);
      }
      if (obj.delivery_type === 1) {
        return (Number(obj.delivery_type_item || 0) + Number(obj.purchase_price || 0))?.toFixed(8);
      }
      if (obj.delivery_type === 2) {
        return obj.delivery_type_item?.toFixed(8);
      }
      return null;
    },

    fieldProps: {
      placeholder: '请输入',
      precision: 8,
      min: 0,
      textAfter: (
        <ProFormDependency name={['state', 'purchase_price', 'deliveryPrice', 'delivery_type', 'delivery_type_item']}>
          {(obj, form) => {
            let value;
            const purchase_price = Number(obj.purchase_price || 0);
            const delivery_type_item = Number(obj.delivery_type_item || 0);
            if (obj.delivery_type == 0) {
              value =
                delivery_type_item && purchase_price
                  ? (((obj.deliveryPrice - purchase_price) / obj.deliveryPrice) * 100).toFixed(2)
                  : '0.00';
            }
            if (obj.delivery_type == 1) {
              value =
                delivery_type_item && purchase_price
                  ? ((delivery_type_item / obj.deliveryPrice) * 100).toFixed(2)
                  : '0.00';
            }
            if (obj.delivery_type == 2) {
              value =
                delivery_type_item && purchase_price
                  ? (((delivery_type_item - purchase_price) / delivery_type_item) * 100).toFixed(2)
                  : '0.00';
            }
            return (
              <Tooltip title={`配送毛利率:${Number(value).toFixed(2)}%`}>
                <span
                  style={{
                    color: Number(value) >= 0 ? 'red' : 'green',
                    marginLeft: '10px',
                  }}
                >
                  <span>{value}</span>
                  <span>%</span>
                </span>
              </Tooltip>
            );
          }}
        </ProFormDependency>
      ),
    },
  },
  {
    tag: 'SCM',
    label: '批发价',
    id: CenterFieldKeyMap.wholesaleCompute,
    name: 'wholesalePrice',
    disabled: true,
    componentType: 'inputNumber',
    dependencies: ['state', 'purchase_price', 'wholesale_type', 'wholesale_type_item'],
    request: (obj) => {
      return obj;
    },
    handleDefaultValue(obj) {
      if (obj.wholesale_type === 0) {
        console.log(obj.wholesale_type_item, obj.purchase_price);
        return ((obj.wholesale_type_item * 0.01 + 1) * Number(obj.purchase_price || 0))?.toFixed(8);
      }
      if (obj.wholesale_type === 1) {
        return (Number(obj.wholesale_type_item || 0) + Number(obj.purchase_price || 0))?.toFixed(8);
      }
      if (obj.wholesale_type === 2) {
        return obj.wholesale_type_item?.toFixed(8);
      }
      return null;
    },
    fieldProps: {
      placeholder: '请输入',
      precision: 8,
      min: 0,
      textAfter: (
        <ProFormDependency
          name={['state', 'purchase_price', 'wholesalePrice', 'wholesale_type', 'wholesale_type_item']}
        >
          {(obj, form) => {
            let value;
            const purchase_price = Number(obj.purchase_price || 0);
            const wholesale_type_item = Number(obj.wholesale_type_item || 0);
            if (obj.wholesale_type == 0) {
              value =
                wholesale_type_item && purchase_price
                  ? (((obj.wholesalePrice - purchase_price) / obj.wholesalePrice) * 100).toFixed(2)
                  : '0.00';
            }
            if (obj.wholesale_type == 1) {
              value =
                wholesale_type_item && purchase_price
                  ? ((wholesale_type_item / obj.wholesalePrice) * 100).toFixed(2)
                  : '0.00';
            }
            if (obj.wholesale_type == 2) {
              value =
                wholesale_type_item && purchase_price
                  ? (((wholesale_type_item - purchase_price) / wholesale_type_item) * 100).toFixed(2)
                  : '0.00';
            }
            return (
              <Tooltip title={`批发毛利率:${Number(value).toFixed(2)}%`}>
                <span
                  style={{
                    color: Number(value) >= 0 ? 'red' : 'green',
                    marginLeft: '10px',
                  }}
                >
                  <span>{value}</span>
                  <span>%</span>
                </span>
              </Tooltip>
            );
          }}
        </ProFormDependency>
      ),
    },
  },
  {
    tag: 'Center',
    label: '斤规格',
    id: CenterFieldKeyMap.specUnit,
    componentType: 'input',
    name: 'weight_spec',
    dependencies: ['options', 'weight_spec_unit'],
    fieldProps: {
      style: { width: '100%' },
      addonAfter: (
        <ProFormDependency name={['options', 'weight_spec']}>
          {(obj, form) => {
            return (
              <ProForm.Item
                noStyle
                name={'weight_spec_unit'}
                rules={[
                  ({ getFieldValue }: any) => ({
                    validator: (_: any, value: any) => {
                      if (!value && obj?.weight_spec !== '0') {
                        return Promise.reject(new Error('请选择单位'));
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <XlbSelect width={80} options={obj.options} placeholder="*包" />
              </ProForm.Item>
            );
          }}
        </ProFormDependency>
      ),
    },
  },
  {
    tag: 'center',
    id: CenterFieldKeyMap.wholesalePriceWithRadio,
    componentType: 'group',
    fieldProps: {
      formList: [
        {
          tag: 'center',
          label: '批发价',
          id: 'storeReminderDays',
          name: 'store_reminder_days',
          componentType: 'inputNumber',
          fieldProps: { precision: 2, min: 0, style: { width: '100%' } },
          textAfter: (
            <ProFormDependency name={['store_reminder_days', 'aa']}>
              {(obj, form) => {
                console.log('store_reminder_days', obj, form);
                return (
                  <div style={{ color: 'red' }}>
                    <span>{obj.store_reminder_days || '0.00'}</span>
                    <span>%</span>
                  </div>
                );
              }}
            </ProFormDependency>
          ),
        },
      ],
    },
  },
  {
    tag: 'center',
    id: CenterFieldKeyMap.standardPriceWithRadio,
    label: '标准售价',
    componentType: 'inputNumber',
    name: 'sale_price',
    fieldProps: {
      placeholder: '请输入',
      precision: 2,
      min: 0,
      textAfter: (
        <ProFormDependency name={['sale_price', 'purchase_price']}>
          {(obj, form) => {
            const value =
              obj.sale_price && obj.purchase_price
                ? (((obj.sale_price - obj.purchase_price) / obj.sale_price) * 100).toFixed(2)
                : '0.00';
            return (
              <Tooltip title={`零售毛利率:${Number(value).toFixed(2)}%`}>
                <span
                  style={{
                    color: Number(value) > 0 ? 'red' : 'green',
                    marginLeft: 10,
                  }}
                >
                  <span>{value}</span>
                  <span>%</span>
                </span>
              </Tooltip>
            );
          }}
        </ProFormDependency>
      ),
    },
  },
  {
    tag: 'center',
    label: '商品属性',
    id: CenterFieldKeyMap.productAttributes,
    name: 'check',
    componentType: 'checkbox',
  },
  {
    tag: 'center',
    label: '组织',
    name: 'org_id',
    id: CenterFieldKeyMap?.centerOrgId,
    fieldProps: {
      treeModalConfig: {
        title: '选择组织',
        url: '/center/hxl.center.org.find',
        dataType: 'lists',
        checkable: false, // 是否多选
        primaryKey: 'id',
        params: { levels: [1, 2] },
      },
      treeConfig: {
        checkStrictly: true,
      },
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!Array.isArray(value)) return;
        return value[0];
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'center',
    label: '公司',
    name: 'org_id',
    id: CenterFieldKeyMap?.centerCompanyAsOrgId,
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      const res = await globalFetch.post(anybaseURL + '/center/hxl.center.org.find', {
        levels: [1, 2],
      });
      if (res?.code === 0) {
        return res.data?.map((item: any) => ({
          ...item,
          label: item.name,
          value: item.id,
        }));
      }
      return [];
    },
    componentType: 'select',
  },
  {
    tag: 'center',
    label: '商品分类',
    id: CenterFieldKeyMap.centerCategoryId,
    fieldProps: {
      treeModalConfig: {
        // disabled: true,
        width: 300,
        title: '选择商品分类', // 标题
        url: '/center/hxl.center.itemcategory.find', // 请求地址
        dataType: 'lists',
        checkable: false, // 是否多选
        primaryKey: 'id',
        afterPost: (data: any) => {
          return data.map((item: any) => {
            return {
              ...item,
              disabled: item?.level !== 3,
            };
          });
        },
      } as any,
    },
    componentType: 'inputDialog',
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!value?.length) return;
        return value[0];
      },
    },
  },
  //新品申请
  {
    tag: 'center',
    label: '采购体积(m³)',
    id: CenterFieldKeyMap?.purchaseVolume,
    name: 'purchase_volume',
    componentType: 'inputNumber',
    dependencies: ['purchase_length', 'purchase_width', 'purchase_height'],
    request: (obj) => {
      return obj;
    },
    handleDefaultValue(obj) {
      const volume = obj.purchase_length * obj.purchase_width * obj.purchase_height * 0.000001;
      return !volume ? '0.0000' : volume?.toFixed(4);
    },
  },
  {
    tag: 'center',
    label: '采购体积(m³)',
    id: CenterFieldKeyMap?.deliveryVolume,
    name: 'delivery_volume',
    componentType: 'inputNumber',
    dependencies: ['purchase_ratio', 'delivery_ratio', 'purchase_volume'],
    request: (obj) => {
      return obj;
    },
    handleDefaultValue(obj) {
      const purchase_ratio = Number(obj.purchase_ratio || 0);
      const delivery_ratio = Number(obj.delivery_ratio || 0);

      const volume =
        ((obj?.purchase_volume / purchase_ratio) * delivery_ratio).toFixed(4) === 'NaN'
          ? '0.0000'
          : ((obj?.purchase_volume / purchase_ratio) * delivery_ratio).toFixed(4);
      return volume;
    },
  },
  //新品申请上传外检报告list
  {
    tag: 'SCM',
    label: '上传',
    id: CenterFieldKeyMap?.externalReportList,
    componentType: 'list',
    fieldProps: {
      createText: '添加',
      formList: [
        {
          componentType: 'upload',
          tag: 'scm',
          id: 'supplier_quality_record_files',
          label: '',
          dependencies: ['state', 'food_safe_audit_state'],
          itemSpan: 1,
          name: 'supplier_quality_record_files',
          fieldProps: {
            action: '/center/hxl.center.newitemapplyorder.file.upload',
            mode: 'textButton',
            listType: 'text',
            accept: ['pdf'],
            hiddenControlerIcon: false,
            deleteByServer: false,
            showUpload: true,
            data: {
              refType: 'SUPPLIER_AUDIT',
              refId: 'sfsffefefe',
            },
          },
        },
        {
          componentType: 'select',
          tag: 'scm',
          id: 'supplier_item',
          label: '生产厂商',
          name: 'supplier_item',
          rules: [{ required: false, message: '请选择生产厂商' }],
          fieldProps: (form, index) => {
            const { supplier_quality_record_data_list } = form.getFieldsValue(true);
            const supplierItem = supplier_quality_record_data_list?.[index?.dataIndex]?.supplier_item;
            return {
              placeholder: '请选择',
              options: form.getFieldValue('producerSupplierOptions'),
              textAfter: supplierItem?.producer_supplier_name && (
                <XlbIcon
                  name="fuzhi"
                  className="custom-form-item-icon"
                  onClick={() => {
                    copy(supplierItem?.producer_supplier_name);
                    message.success('复制成功');
                  }}
                />
              ),
              className: 'custom-form-item',
            };
          },
          formItemProps: {
            labelCol: {
              span: 2,
            },
            labelAlign: 'left',
            getValueFromEvent(a, b, c) {
              return {
                producer_supplier_id: b?.value,
                producer_supplier_name: b?.label,
                origin_place: b?.origin_place,
              };
            },
            getValueProps(value) {
              return { value: value?.producer_supplier_id };
            },
          },
        },
        {
          componentType: 'datePicker',
          tag: 'scm',
          label: '外检报告签发日期',
          id: 'valid_date',
          name: 'valid_date',
          dependencies: ['state', 'food_safe_audit_state'],
          fieldProps: {
            placeholder: '请选择日期',
          },
          rules: [{ required: false, message: '请选择外检报告签发日期' }],
        },
        {
          componentType: 'select',
          tag: 'scm',
          label: '外检报告截止时间',
          id: 'end_date',
          name: 'end_date',
          dependencies: ['state'],
          fieldProps: {
            placeholder: '请选择外检报告截止时间',
            options: MONTHS_OPTION,
          },
          rules: [{ required: false, message: '请选择外检报告截止时间' }],
        },
        {
          componentType: 'input',
          tag: 'scm',
          label: '产地',
          id: 'origin_place',
          name: 'origin_place',
          dependencies: ['supplier_item'],
          fieldProps: {
            placeholder: '请输入',
          },
          rules: [{ required: false, message: '请输入产地' }],
          request: (params) => {
            console.log('supplier_item', params.supplier_item);
            return params?.supplier_item?.origin_place;
          },
          handleDefaultValue: (data: any) => {
            return data;
          },
        },
      ],
    },
  },
  //新品申请上传外检报告个别可编辑
  // @ts-ignore
  {
    tag: 'SCM',
    label: '上传',
    id: CenterFieldKeyMap?.disabledExternalReportList,
    componentType: 'list',
    fieldProps: {
      createText: null,
      formList: [
        {
          componentType: 'upload',
          tag: 'scm',
          id: 'supplier_quality_record_files',
          label: '',
          dependencies: ['food_safe_audit_state'],
          itemSpan: 1,
          name: 'supplier_quality_record_files',
          fieldProps: {
            action: '/center/hxl.center.newitemapplyorder.file.upload',
            mode: 'textButton',
            listType: 'text',
            accept: ['pdf'],
            hiddenControlerIcon: false,
            deleteByServer: false,
            showUpload: true,
            data: {
              refType: 'SUPPLIER_AUDIT',
              refId: 'sfsffefefe',
            },
          },
        },
        {
          componentType: 'select',
          tag: 'scm',
          id: 'supplier_item',
          label: '生产厂商',
          name: 'supplier_item',
          dependencies: ['imports'],
          rules: [
            ({ getFieldsValue }: any) => ({
              required: !getFieldsValue(true)?.imports,
              message: '请选择生产厂商',
            }),
          ],
          fieldProps: (form, index) => {
            const { supplier_quality_record_data_list } = form.getFieldsValue(true);
            const supplierItem = supplier_quality_record_data_list?.[index?.dataIndex]?.supplier_item;
            return {
              placeholder: '请选择',
              options: form.getFieldValue('producerSupplierOptions'),
              textAfter: supplierItem?.producer_supplier_name && (
                <XlbIcon
                  name="fuzhi"
                  className="custom-form-item-icon"
                  onClick={() => {
                    copy(supplierItem?.producer_supplier_name);
                    message.success('复制成功');
                  }}
                />
              ),
              className: 'custom-form-item',
            };
          },
          formItemProps: {
            getValueFromEvent(a, b, c) {
              return {
                producer_supplier_id: b?.value,
                producer_supplier_name: b?.label,
                origin_place: b?.origin_place,
              };
            },
            getValueProps(value) {
              return { value: value?.producer_supplier_id };
            },
          },
        },
        {
          componentType: 'datePicker',
          tag: 'scm',
          label: '外检报告签发日期',
          id: 'valid_date',
          name: 'valid_date',
          dependencies: ['food_safe_audit_state', 'imports'],
          fieldProps: {
            placeholder: '请选择外检报告签发日期',
          },
          rules: [
            ({ getFieldsValue }: any) => ({
              required: !getFieldsValue(true)?.imports,
              message: '请选择外检报告签发日期',
            }),
          ],
        },
        {
          componentType: 'select',
          tag: 'scm',
          label: '外检报告截止时间',
          id: 'end_date',
          name: 'end_date',
          dependencies: ['food_safe_audit_state', 'imports'],
          fieldProps: {
            placeholder: '请选择外检报告截止时间',
            options: MONTHS_OPTION,
          },
          rules: [
            ({ getFieldsValue }: any) => ({
              required: !getFieldsValue(true)?.imports,
              message: '请选择外检报告截止时间',
            }),
          ],
        },
        {
          componentType: 'input',
          tag: 'scm',
          label: '产地',
          id: 'origin_place',
          name: 'origin_place',
          dependencies: ['supplier_item', 'imports'],
          fieldProps: {
            placeholder: '请输入',
          },
          rules: [
            ({ getFieldsValue }: any) => ({
              required: !getFieldsValue(true)?.imports,
              message: '请输入产地',
            }),
          ],
          request: (params) => {
            console.log('supplier_item', params.supplier_item);
            return params?.supplier_item?.origin_place;
          },
          handleDefaultValue: (data: any) => {
            return data;
          },
        },
      ],
      itemRender: ({ listDom }, listMeta) => {
        return listDom;
      },
    },
  },
  {
    componentType: 'select',
    tag: 'center',
    id: CenterFieldKeyMap.operationTypeList,
    async request(formValues: any, anybaseURL?: any, globalFetch?: any) {
      console.log(anybaseURL);
      const res = await globalFetch.post(anybaseURL + '/mdm/manage.userlog.select.list', {
        ...formValues,
      });
      if (res.code === 0) {
        return res.data.map((item: any) => ({
          label: item,
          value: item,
        }));
      }
      return [];
    },
  },
  //外检报告全部不校验
  {
    tag: 'SCM',
    label: '上传',
    id: CenterFieldKeyMap?.externalReportListNoRules,
    componentType: 'list',
    fieldProps: {
      createText: '添加',
      formList: [
        {
          componentType: 'upload',
          tag: 'scm',
          id: 'supplier_quality_record_files',
          label: '',
          dependencies: ['state'],
          itemSpan: 1,
          name: 'supplier_quality_record_files',
          fieldProps: {
            action: '/center/hxl.center.newitemapplyorder.file.upload',
            mode: 'textButton',
            listType: 'text',
            accept: ['pdf'],
            hiddenControlerIcon: false,
            deleteByServer: false,
            showUpload: true,
            data: {
              refType: 'SUPPLIER_AUDIT',
              refId: 'sfsffefefe',
            },
          },
        },
        {
          componentType: 'select',
          tag: 'scm',
          id: 'supplier_item',
          label: '生产厂商',
          name: 'supplier_item',
          fieldProps: (form, index) => {
            const { supplier_quality_record_data_list } = form.getFieldsValue(true);
            const supplierItem = supplier_quality_record_data_list?.[index?.dataIndex]?.supplier_item;
            return {
              placeholder: '请选择',
              options: form.getFieldValue('producerSupplierOptions'),
              textAfter: supplierItem?.producer_supplier_name && (
                <XlbIcon
                  name="fuzhi"
                  className="custom-form-item-icon"
                  onClick={() => {
                    copy(supplierItem?.producer_supplier_name);
                    message.success('复制成功');
                  }}
                />
              ),
              className: 'custom-form-item',
            };
          },

          formItemProps: {
            labelCol: {
              span: 8,
            },
            labelAlign: 'right',
            getValueFromEvent(a, b, c) {
              return {
                producer_supplier_id: b?.value,
                producer_supplier_name: b?.label,
                origin_place: b?.origin_place,
              };
            },
            getValueProps(value) {
              return { value: value?.producer_supplier_id };
            },
          },
        },
        {
          componentType: 'datePicker',
          tag: 'scm',
          label: '外检报告签发日期',
          id: 'valid_date',
          name: 'valid_date',
          dependencies: ['state', 'food_safe_audit_state'],
          fieldProps: {
            placeholder: '请选择日期',
          },
        },
        {
          componentType: 'select',
          tag: 'scm',
          label: '外检报告截止时间',
          id: 'end_date',
          name: 'end_date',
          dependencies: ['state'],
          fieldProps: {
            placeholder: '请选择外检报告签发日期',
            options: MONTHS_OPTION,
          },
        },
        {
          componentType: 'input',
          tag: 'scm',
          label: '产地',
          id: 'origin_place',
          name: 'origin_place',
          dependencies: ['supplier_item'],
          fieldProps: {
            placeholder: '请输入',
          },
          request: (params) => {
            return params?.supplier_item?.origin_place;
          },
          handleDefaultValue: (data: any) => {
            return data;
          },
        },
      ],
    },
  },
  {
    tag: 'center',
    id: CenterFieldKeyMap.foodsumdays,
    componentType: 'inputNumber',
    name: 'food_safe_audit_oppose_deadline_days',
    colon: false,
    itemSpan: 10,
    fieldProps: {
      suffix: '天',
      min: 1,
      precision: 0,
    },
    formItemProps: {
      colon: false,
      labelAlign: 'left',
      labelCol: { style: { width: 200 } },
    },
  },
  // {
  //   tag: 'FSMS',
  //   label: '业务区域',
  //   id: CenterFieldKeyMap?.BUSINESS,
  //   name: 'org_ids1',
  //   componentType: 'inputDialog',
  //   fieldProps: {
  //     treeModalConfig: {
  //       title: '选择业务区域',
  //       url: '/erp/hxl.erp.org.tree',
  //       dataType: 'lists',
  //       checkable: true, // 是否多选
  //       primaryKey: 'id',
  //     },
  //   },
  // },
  // {
  //   tag: 'FSMS',
  //   label: '行政区域',
  //   id: CenterFieldKeyMap?.ADMINISTRATIVE,
  //   name: 'org_ids2',
  //   componentType: 'inputDialog',
  //   fieldProps: {
  //     treeModalConfig: {
  //       title: '选择行政区域',
  //       url: '/erp/hxl.erp.org.tree',
  //       dataType: 'lists',
  //       checkable: true, // 是否多选
  //       primaryKey: 'id',
  //     },
  //   },
  // },
  //一级二级 万辰集团
  {
    tag: 'center',
    label: '组织',
    id: CenterFieldKeyMap?.firstSecondLevelOrgIds,
    name: 'org_id',
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      const res = await globalFetch.post(anybaseURL + '/center/hxl.center.org.find', {
        levels: [1, 2],
      });
      if (res?.code === 0) {
        return res.data.map((item: any) => ({
          ...item,
          label: item.name,
          value: item.id,
        }));
      }
      return [];
    },
    componentType: 'select',
  },
  {
    tag: 'SCM',
    id: CenterFieldKeyMap?.formSpanItemLarge,
    componentType: 'upload',
    formItemProps: {
      labelCol: {
        span: 13,
      },
      labelAlign: 'right',
    },
  },
];
