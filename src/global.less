@import 'styles/antd';
@import 'styles/alitable';
@font-face {
  font-family: 'NUM';
  src: local('helvetica'), local('arial'), local('verdana'), local('sans-serif');
  unicode-range: U+30-39, U+61-7a, U+41-5a;
}

html {
  overflow: hidden;
}

// 通用样式
html #root-master {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: auto;
  font-weight: 400;
  font-size: 13px;
  font-family: 'NUM', 'PingFang SC', helvetica, arial, verdana, sans-serif;
  background-color: @color_fff;
}

p {
  margin: 0;
}

.container {
  height: calc(100vh - 78px);
  overflow: auto;
}

// 常用样式
.border {
  border: 1px solid red;
}

.v-box5 {
  height: @size_5;
}

.v-box10 {
  height: @size_10;
}

.h-box5 {
  width: @size_5;
}

.h-box10 {
  width: @size_10;
}

.cursors {
  cursor: pointer;
}

.full-box {
  width: 100%;
  height: 100%;
}

.v_padding10 {
  padding: @size_10 0 0 0;
}

.top-margin20 {
  margin-top: @size_20;
}

.top-margin10 {
  margin-top: @size_10;
}

.left-margin10 {
  margin-left: @size_10;
}

.left-margin20 {
  margin-left: @size_20;
}

.right-margin10 {
  margin-right: @size_10;
}

.right-margin20 {
  margin-right: @size_20;
}

// flex布局样式
.row-flex {
  display: flex;
  flex-direction: row;
}

.flex-wrap {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.col-flex {
  display: flex;
  flex-direction: column;
}

.h-flex {
  display: flex;
  justify-content: center;
}

.v-flex {
  display: flex;
  align-items: center;
}

.c-flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

.link {
  color: #3d66fe;
  text-decoration: underline;
  cursor: pointer;
}

.info {
  color: #1d2129;
}

.default {
  color: #3d66fe;
}

.warning {
  color: #ff7d01;
}

.success {
  color: #00b42b;
}

.danger {
  color: #ff0000;
}

.invalid {
  color: #86909c;
}

.purple {
  color: #cc66cc;
}

.default_bg {
  background-color: @color_default;
}

.warning_bg {
  background-color: #ff7d01;
}

.success_bg {
  background-color: @color_success;
}

.danger_bg {
  background-color: @color_danger;
}

.purple_bg {
  background-color: @color_purple;
}

.theme {
  color: @color_theme;
}

.button_box {
  padding: 0;
  border-bottom: 1px solid @color_line2;
}

.button_box_top {
  padding: 0 0 8px 16px;
  border-top: 1px solid @color_line5;
}

.button_box_top_tool {
  padding: 0 0 8px 16px;
  border-top: 1px solid @color_line5;
}

.ant-upload-list-picture-card .ant-upload-list-item-actions {
  width: 48px;
  text-align: center;
}

// 蓝色横条样式
.tabBlock {
  display: flex;
  align-items: center;
  height: 25px;
  padding: 0 10px;
  color: @color_fff;
  background: @color_theme;
}

// 左侧分类样式
.leftGoods {
  float: left;
  width: 230px;
  height: 100%;
  overflow: hidden;
  background: #fff;
  border-right: 1px solid #ccc;
}

//右侧分类样式
.ant-layout-content {
  background-color: #fff;
}

.left_context {
  margin-top: 5px;
  margin-left: 10px;
}

.form_header_box {
  box-sizing: border-box;
  padding: 3px 0 3px 0;
}

.new_form_header_box {
  box-sizing: border-box;
  padding: 9px 0 5px 0;
}

tfoot .next-checkbox-wrapper {
  display: none;
}

.ant-tabs {
  width: 100%;
}

.ant-tabs-nav-wrap {
  margin-left: 10px;
}

.react-resizable {
  position: relative;
  background-clip: padding-box;
}

.react-resizable-handle {
  position: absolute;
  right: -5px;
  bottom: 0;
  z-index: 1;
  width: 10px;
  height: 100%;
  cursor: col-resize;
}

//form表单 disabled状态下背景变化
.ant-input[disabled] {
  color: #86908c;
  background-color: rgb(242, 243, 245) !important;
  cursor: no-drop;
}

.ant-input-affix-wrapper-disabled {
  color: #86908c;
  background-color: rgb(242, 243, 245) !important;
  cursor: no-drop;
}

.ant-select-disabled.ant-select-multiple .ant-select-selection-item {
  color: #86908c;
  background-color: rgb(242, 243, 245) !important;
  cursor: no-drop;
}

.ant-radio-disabled + span {
  color: #86908c !important;
}

.ant-checkbox-disabled + span {
  color: #86908c !important;
}

.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  color: #86908c;
  background-color: rgb(242, 243, 245) !important;
  cursor: no-drop;
}

.ant-input-number-disabled {
  color: #86908c;
}

.ant-picker.ant-picker-disabled {
  color: #86908c;
  background-color: rgb(242, 243, 245) !important;
  cursor: no-drop;
}

.ant-input-affix-wrapper {
  padding: 0 7px !important;
}

.ant-input-affix-wrapper-sm {
  padding: 0 7px 0 !important;
}

.ant-select-single:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input {
  height: auto;
}
.ant-select-single .ant-select-selector .ant-select-selection-search {
  top: -2px;
  left: 7px;
}
.ant-select-selector {
  height: 26px !important;
  padding: 0 7px !important;
}

.ant-input-sm {
  height: 26px !important;
  padding: 0 7 px !important;
}

.ant-input .ant-form-item {
  font-size: 13px !important;
}

input {
  font-size: 13px !important;
}

.ant-picker {
  height: 26px !important;
  padding: 0 7px !important;
}

#htmlContainer {
  .media-wrap {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .media-wrap > img {
    position: relative;
    width: 100% !important;
    height: 100% !important;
  }
}

//修复弹框关闭按钮高度问题
.ant-modal-close-x {
  width: 16px;
  height: 16px;
  line-height: 16px;
}

//解决dropdown高度不固定无法控制弹出位置的问题
.ant-dropdown-menu {
  max-height: 240px;
  overflow: auto;
}

.ant-progress-success-bg,
.ant-progress-bg {
  background-color: @color_theme;
}
.iconfont {
  cursor: pointer;
}
.ant-select-dropdown {
  z-index: 9999 !important;
  .ant-progress-success-bg,
  .ant-progress-bg {
    background-color: @color_theme;
  }
}
.xlb-field-item-after {
  z-index: 1;
}
