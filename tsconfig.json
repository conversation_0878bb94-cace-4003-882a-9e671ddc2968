{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "experimentalDecorators": true, // 启用实验性的ES装饰器
    "lib": [
      "ES2020",
      "DOM",
      "DOM.Iterable"
    ],
    "module": "ESNext",
    "skipLibCheck": true,
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@@/*": [
        "./*"
      ]
    },
    "typeRoots": [
      "node_modules/@types",
      "src/typings",
      "node_modules/@xlb/components",
      "node_modules/@xlb/utils"
    ]
  },
  "include": [
    "src/*"
  ],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ],
}