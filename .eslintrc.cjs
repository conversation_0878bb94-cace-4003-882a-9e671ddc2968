module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: [
    'eslint:recommended',
    'prettier',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react/jsx-runtime',
    'plugin:react-hooks/recommended',
    'plugin:promise/recommended',
  ],
  ignorePatterns: ['dist', '.eslintrc.cjs'],
  overrides: [
    {
      env: {
        node: true
      },
      files: [
        '.eslintrc.{js,cjs}'
      ],
      parserOptions: {
        // project: './tsconfig.json',
        sourceType: 'module'
      }
    }
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    'ecmaVersion': 'latest',
    'sourceType': 'module'
  },
  plugins: [
    'react-refresh',
    'prettier',
  ],
  rules: {
    indent: ['error', 2], // 用于指定代码缩进的方式，这里配置为使用2个空格进行缩进。
    eqeqeq: 2,//必须使用全等
    quotes: ['error', 'single'], // 用于指定字符串的引号风格，这里配置为使用单引号作为字符串的引号。
    semi: ['error', 'always'], //用于指定是否需要在语句末尾添加分号，这里配置为必须始终添加分号。
    'eol-last': ['error', 'always'],
    'key-spacing': ['error', { mode: 'strict' }],
    'comma-spacing': ['error', { 'before': false, 'after': true }],
    'comma-dangle': ['error', 'never'],//对象字面量项尾禁止逗号
    'linebreak-style': [0, 'error', 'windows'], // 用于指定换行符的风格，这里配置为使用 Windows 风格的换行符（\r\n）。
    '@typescript-eslint/no-explicit-any': ['off'], // 用于配置 TypeScript 中的 'any' 类型的使用规则，这里配置为关闭禁止显式使用 'any' 类型的检查。
    'react-refresh/only-export-components': [
      'warn',
      { allowConstantExport: true },
    ],
  },
  settings: {
    react: {
      /**
      * 'detect' automatically picks the version you have installed.
      * You can also use `16.0`, `16.3`, etc, if you want to override the detected value.
      * default to latest and warns if missing 
      */
      version: '18.2.0'   // It will default to 'detect' in the future
    }
  }
}
