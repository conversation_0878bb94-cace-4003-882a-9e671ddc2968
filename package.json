{"name": "center_web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "npm run dev", "dev": "vite", "release": "node ./bin/release.mjs", "build:test": "node --max-old-space-size=4096 ./node_modules/vite/bin/vite build --mode=test", "build": "node --max-old-space-size=4096 ./node_modules/vite/bin/vite build --mode=production", "lint": "eslint . --ext ts,tsx,js,jsx,cjs --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "prepare": "husky install", "commit": "git-cz", "commitmsg": "commitlint --config commitlint.config.cjs -e -V", "build:icon": "npx iconfont-h5", "precommit": "node  ./createlocalfile.js"}, "dependencies": {"@alifd/next": "^1.27.2", "@ant-design/icons": "^5.2.6", "@ant-design/pro-components": "2.6.48", "@ant-design/pro-form": "^2.31.3", "@antv/x6": "^2.18.1", "@antv/x6-react-shape": "^2.2.3", "@ebay/nice-modal-react": "^1.2.13", "@reduxjs/toolkit": "^2.0.1", "@types/node": "^20.10.5", "@types/react-router-dom": "^5.3.3", "@xlb/components": "2.0.134", "@xlb/datav": "0.1.40", "@xlb/utils": "1.9.0", "ahooks": "^3.7.8", "ali-react-table": "^2.6.1", "antd": "5.11.3", "axios": "^1.6.2", "braft-editor": "^2.3.9", "class-validator": "^0.14.0", "classnames": "^2.5.0", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.10", "decimal.js": "^10.5.0", "history": "^5.3.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.30.1", "prop-types": "^15.8.1", "pubsub-js": "^1.9.4", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-activation": "^0.12.4", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-flow-builder": "^2.8.0", "react-router-dom": "^6.20.1", "react-sortable-hoc": "^2.0.0", "uuid": "^10.0.0", "wrap-ansi": "^9.0.0", "zustand": "^4.4.7"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.7", "@types/pubsub-js": "^1.8.6", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "@vitejs/plugin-react": "^4.0.3", "commitizen": "4.3.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "husky": "^8.0.3", "less": "^4.2.0", "lint-staged": "^15.2.0", "pre-push": "^0.1.4", "prettier": "^3.1.0", "react-iconfont-cli": "^2.0.2", "typescript": "^5.3.2", "vite": "^4.4.5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["npm run lint", "git add"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "resolutions": {"wrap-ansi": "^7.0.0"}}