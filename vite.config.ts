import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
//@ts-ignore
import buildUpdateTimePlugin from './src/utils/createlocalfile';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  const { VITE_OSS_URL } = env;
  return {
    optimizeDeps: {
      esbuildOptions: {
        define: {
          global: 'globalThis',
        },
      },
    },
    build: {
      assetsDir: './assets',
      sourcemap: false,
      rollupOptions: {
        plugins: [buildUpdateTimePlugin()],
      },
    },
    resolve: {
      alias: [
        {
          find: '@',
          replacement: path.resolve(__dirname, '.', 'src'),
        },
        {
          find: '@@',
          replacement: path.resolve(__dirname, '.'),
        },
        {
          find: /^~/,
          replacement: '',
        },
      ],
    },
    base: VITE_OSS_URL,
    server: {
      host: 'local.xlbsoft.com',
      port: 5188,
      allowedHosts: true,
      hmr: {
        overlay: false,
      },
      proxy: {
        '/mdm': {
          target: 'http://mdm.mdm.ali-test.xlbsoft.com/',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api\/mdm/, '/mdm'),
        },
        '/center': {
          target: 'http://center.center.ali-test.xlbsoft.com/',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
        '/erp': {
          target: 'https://react-web.react-web.ali-test.xlbsoft.com/',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
      },
    },
    css: {
      modules: {
        hashPrefix: 'prefix',
        generateScopedName: '[name]__[local]__[hash:base64:5]',
      },
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          // https://ant.design/docs/react/customize-theme-cn
          modifyVars: {
            '@color_fff': '#FFFFFF',
            '@color_header': '#2069D1',
            '@color_tab': '#F4F7FA',
            '@color_main': '#F4F7FA',
            '@color_home': '#EFF2F7',
            '@color_link': '#3D66FE',
            '@color_disabled': '#F2F3F5',

            '@page_bg': '#F3F4F7',

            // 主色
            '@color_theme': '#3D66FE',
            '@color_theme_bg': '#E8F1FF',

            // 按钮，辅助图形，图标
            '@color_init': '#1D2129',
            '@color_default': '#3D66FE',
            '@color_danger': '#F53F3F',
            '@color_invalid': '#86909C',
            '@color_warning': '#FF7D01',
            '@color_success': '#00B42B',
            '@color_purple': '#CC66CC',
            // 边框、分栏
            '@color_line1': '#393D49',
            '@color_line2': '#E5E6EA',
            '@color_line3': '#F5F7FA',
            '@color_line4': '#DCDFE6',
            '@color_line5': '#ECEDF0',
            // 文字
            color_black1: '#000',
            color_black2: '#333',
            color_black3: '#666',
            color_black4: '#999',

            '@size_5': '5px',
            '@size_10': '10px',
            '@size_12': '12px',
            '@size_14': '14px',
            '@size_16': '16px',
            '@size_18': '18px',
            '@size_20': '20px',
          },
        },
      },
    },
    plugins: [react()],
  };
});
